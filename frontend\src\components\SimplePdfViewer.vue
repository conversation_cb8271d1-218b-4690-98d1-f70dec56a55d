<template>
  <div class="simple-pdf-viewer">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <span class="pdf-title">PDF 文档</span>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="openInNewTab" type="primary">
          <el-icon><Link /></el-icon>
          新窗口打开
        </el-button>
      </div>
    </div>
    
    <div class="pdf-container">
      <div class="pdf-loading" v-if="loading">
        <el-icon class="loading-spin"><Loading /></el-icon>
        <p>正在加载PDF...</p>
      </div>

      <div class="pdf-error" v-else-if="error">
        <el-icon><Warning /></el-icon>
        <p>PDF加载失败</p>
        <p class="error-detail">{{ error }}</p>
        <el-button @click="retryLoad" type="primary">重试</el-button>
        <el-button @click="openInNewTab">在新窗口中打开</el-button>
      </div>
      
      <!-- 使用 iframe 显示PDF -->
      <iframe
        v-else
        :src="pdfUrl + '#toolbar=1&navpanes=1&scrollbar=1'"
        class="pdf-iframe"
        @load="handleLoad"
        @error="handleError"
        frameborder="0"
        allowfullscreen
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Link, 
  Loading,
  Warning
} from '@element-plus/icons-vue'

interface Props {
  pdfUrl?: string
}

const props = defineProps<Props>()

// State
const loading = ref(true)
const error = ref('')

// Methods
const handleLoad = () => {
  loading.value = false
  error.value = ''
}

const handleError = (event?: Event) => {
  loading.value = false
  console.warn('PDF loading error:', event)
  error.value = '此PDF文件可能不支持在浏览器中直接显示，请尝试在新窗口中打开'
}

const openInNewTab = () => {
  if (props.pdfUrl) {
    window.open(props.pdfUrl, '_blank')
  }
}

const retryLoad = () => {
  loading.value = true
  error.value = ''
  // 强制重新加载
  setTimeout(() => {
    if (loading.value) {
      handleError()
    }
  }, 8000) // 增加到8秒
}

// Watchers
watch(() => props.pdfUrl, () => {
  if (props.pdfUrl) {
    loading.value = true
    error.value = ''
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  // 设置加载超时
  setTimeout(() => {
    if (loading.value) {
      handleError()
    }
  }, 8000) // 8秒超时
})
</script>

<style lang="scss" scoped>
.simple-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  
  .toolbar-left {
    .pdf-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
}

.pdf-container {
  flex: 1;
  position: relative;
  min-height: 0;
}

.pdf-loading,
.pdf-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  color: #666;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .loading-spin {
    animation: spin 1s linear infinite;
  }
  
  .error-detail {
    font-size: 12px;
    color: #999;
    margin: 8px 0 16px 0;
    text-align: center;
    max-width: 300px;
  }
  
  .el-button {
    margin: 4px;
  }
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  border-radius: 4px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
