# Transformer多头注意力知识点问答

## 问题1：什么是自注意力机制的核心作用？
**解答**：  
自注意力机制的核心作用是**捕捉序列内部的依赖关系**。通过计算输入序列中每个元素与其他所有元素的关联权重，动态生成每个位置的上下文感知表示。例如在句子"The animal didn't cross the street because it was too tired"中，自注意力能帮助模型确定"it"与"animal"的强关联。

## 问题2：为什么需要缩放因子$\sqrt{d_k}$？
**解答**：  
缩放因子$\sqrt{d_k}$主要解决两个问题：
1. ​**梯度稳定**：当$d_k$较大时，点积结果可能过大，导致softmax进入饱和区（梯度消失）
2. ​**方差控制**：假设$q$和$k$是均值为0、方差为1的独立随机变量，$q \cdot k$的方差为$d_k$，缩放后方差恢复为1

## 问题3：Q/K/V矩阵分别代表什么？
**解答**：  
- ​**Q (Query)**：当前关注点的特征表示，用于查询与其他位置的关系  
- ​**K (Key)**：所有位置的索引特征，用于被查询匹配  
- ​**V (Value)**：实际的特征信息，根据注意力权重进行聚合  
*类比理解*：就像字典查询，Q是查询词，K是字典索引，V是词条解释内容

## 问题4：多头注意力的设计动机是什么？
**解答**：  
多头注意力（Multi-Head Attention）通过以下方式增强模型能力：
1. ​**并行关注不同子空间**：每组头学习不同的注意力模式（如局部/全局、语法/语义）
2. ​**提升表示能力**：$h$个头产生$h$组不同的上下文表示，最后拼接融合
3. ​**计算效率**：分割到多个头后每个头的维度降低为$d_{model}/h$，总计算量与单头相当

## 问题5：如何理解注意力权重矩阵的生成过程？
**解答**：  
权重矩阵生成分四步：
1. ​**相似度计算**：$QK^T$得到原始关联分数矩阵（$n \times n$）
2. ​**缩放调整**：除以$\sqrt{d_k}$防止数值爆炸
3. ​**归一化**：softmax按行归一化得到概率分布
4. ​**信息聚合**：权重矩阵与$V$相乘，实现加权求和

## 问题6：自注意力与RNN处理长序列的差异？
**解答**：  
| 特性               | 自注意力                     | RNN                      |
|--------------------|-----------------------------|--------------------------|
| 依赖距离           | $O(1)$路径长度              | $O(n)$顺序处理           |
| 并行性             | 完全并行                    | 序列依赖                 |
| 内存消耗           | $O(n^2)$显存                | $O(n)$                  |
| 远程依赖           | 直接建模                    | 需通过多个时间步传递     |
| 解释性             | 可可视化注意力权重          | 隐状态难以解释          |
# Transformer多头注意力知识点测验

## 选择题
1. 自注意力机制的核心作用是？  
   A. 降低计算复杂度  
   B. 捕捉序列内部依赖关系  
   C. 减少模型参数量  
   D. 加速收敛速度  
   ​**答案：B**

2. 缩放因子$\sqrt{d_k}$的主要作用是？  
   A. 提高计算效率  
   B. 防止梯度消失和保持方差稳定  
   C. 增加非线性表达能力  
   D. 减少内存消耗  
   ​**答案：B**

3. 在QKV矩阵中，Key矩阵(K)负责：  
   A. 存储要提取的特征信息  
   B. 作为被查询匹配的索引  
   C. 表示当前关注点  
   D. 进行维度变换  
   ​**答案：B**

## 判断题
1. 自注意力机制的计算复杂度与序列长度成线性关系。  
   ​**答案：×**​（应为平方关系$O(n^2)$）

2. 多头注意力的每个头会学习完全相同的注意力模式。  
   ​**答案：×**​（不同头学习不同模式）

3. softmax操作是在计算注意力权重时按列进行的。  
   ​**答案：×**​（按行归一化）

## 填空题
1. 缩放点积注意力的完整计算公式是：______  
   ​**答案：$\text{softmax}(\frac{QK^T}{\sqrt{d_k}})V$**

2. 当$d_k=64$时，缩放因子应该取______  
   ​**答案：8**​（$\sqrt{64}=8$）

3. 在计算注意力时，Q矩阵的维度是$n×d_k$，K矩阵的维度是$m×d_k$，则$QK^T$结果的维度是______  
   ​**答案：$n×m$**

## 应用题
1. 给定：
   - Q = [1, 0.5]  
   - K = [[0.8, 0.2], [0.3, 0.7]]  
   - V = [[1,0], [0,1]]  
   - $d_k=2$  
   计算Attention(Q,K,V)结果  
   ​**解答步骤**：  
   1) $QK^T=[1×0.8+0.5×0.2, 1×0.3+0.5×0.7]=[0.9, 0.65]$  
   2) 缩放：$[0.9, 0.65]/\sqrt{2}≈[0.636, 0.460]$  
   3) softmax：$[e^{0.636}/(e^{0.636}+e^{0.460}),...]≈[0.543, 0.457]$  
   4) 加权求和：$0.543×[1,0]+0.457×[0,1]≈[0.543, 0.457]$  
   ​**最终答案**：[0.543, 0.457]

# Transformer多头注意力30题测验 (6:3:1难度分布)

## █ 选择题 (1-10题)
1. 【简单】自注意力机制主要用于：  
   A) 降低计算成本  
   B) 捕捉序列内部关系  
   C) 减少参数数量  
   ​**答案：B**

2. 【简单】QKV中的V矩阵存储的是：  
   A) 待匹配的特征  
   B) 实际的特征信息  
   C) 位置编码  
   ​**答案：B**

3. 【简单】缩放因子$\sqrt{d_k}$主要用于：  
   A) 加速计算  
   B) 稳定梯度  
   C) 增加非线性  
   ​**答案：B**

4. 【中等】多头注意力的头数增加会导致：  
   A) 每个头的维度降低  
   B) 总计算量平方增长  
   C) 必须使用残差连接  
   ​**答案：A**

5. 【中等】当$d_k=256$时，缩放因子为：  
   A) 16  
   B) 32  
   C) 64  
   ​**答案：A**

6. 【中等】softmax操作在注意力中沿哪个轴计算：  
   A) 列方向  
   B) 行方向  
   C) 矩阵对角线  
   ​**答案：B**

7. 【困难】若Q∈ℝ^{10×64}, K∈ℝ^{20×64}，则$QK^T$的形状为：  
   A) 10×20  
   B) 20×10  
   C) 64×64  
   ​**答案：A**

8. 【困难】下列哪种情况不需要重新计算注意力权重：  
   A) 输入序列长度变化  
   B) 隐藏维度变化  
   C) batch size变化  
   ​**答案：C**

9. 【简单】多头注意力的输出是各头结果的：  
   A) 求和  
   B) 拼接  
   C) 平均  
   ​**答案：B**

10. 【简单】自注意力最适合处理：  
    A) 图像分类  
    B) 时序数据  
    C) 结构化数据  
    ​**答案：B**

## █ 判断题 (11-20题)
11. 【简单】自注意力可以完全替代RNN ✓  
    ​**答案：×** (各有利弊)

12. 【简单】Q和K的维度必须相同 ✓  
    ​**答案：✓**

13. 【中等】多头注意力的计算量随头数线性增长 ✓  
    ​**答案：×** (总计算量不变)

14. 【中等】softmax会使最大权重接近1 ✓  
    ​**答案：✓**

15. 【困难】当$d_k\to∞$时需去掉缩放因子 ✓  
    ​**答案：×** (更需要缩放)

16. 【简单】V矩阵的维度可以与Q不同 ✓  
    ​**答案：✓**

17. 【中等】因果掩码用于限制未来信息 ✓  
    ​**答案：✓**

18. 【困难】自注意力层本身具有位置感知能力 ✓  
    ​**答案：×** (需额外位置编码)

19. 【简单】更多注意力头总是提升模型性能 ✓  
    ​**答案：×** (存在收益递减)

20. 【中等】解码器的交叉注意力也使用QKV机制 ✓  
    ​**答案：✓**

## █ 填空题 (21-30题)
21. 【简单】缩放点积注意力公式：______  
    ​**答案：$\text{softmax}(\frac{QK^T}{\sqrt{d_k}})V$**

22. 【中等】当$d_k=128$，缩放因子=______  
    ​**答案：$8\sqrt{2}$**

23. 【困难】若注意力权重矩阵有2个完全相同的行，说明______  
    ​**答案：对应位置的Q向量相同**

24. 【简单】8头注意力中，每个头的维度是$d_{model}/$______  
    ​**答案：8**

25. 【中等】计算注意力时的padding mask用于忽略______  
    ​**答案：填充符号**

26. 【简单】QKV矩阵是通过______产生的  
    ​**答案：线性变换**

27. 【困难】令$A=QK^T$，$\frac{\partial L}{\partial Q}=$______  
    ​**答案：$\frac{\partial L}{\partial A}K$**

28. 【中等】多头注意力的参数量是单头的______倍  
    ​**答案：h (头数)**

29. 【简单】Transformer中注意力层的典型输出维度是______  
    ​**答案：$d_{model}$**

30. 【困难】若$QK^T$的均值为0，方差为$d_k$，则缩放后的方差为______  
    ​**答案：1**