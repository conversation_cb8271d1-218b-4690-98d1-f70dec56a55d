# AI 服务提示词模板系统

## 概述

本模板系统将 AI 服务中使用的各种提示词提取到统一的模板文件中，便于维护和管理。

## 文件结构

- `prompt_templates.py` - 主要的提示词模板类
- `ai_service.py` - 使用模板的 AI 服务类
- `test_prompt_templates.py` - 模板功能测试文件

## 主要功能

### 1. 系统消息模板

提供不同类型的系统消息：

```python
from app.services.prompt_templates import prompt_templates

# 获取知识助手系统消息
knowledge_msg = prompt_templates.get_system_message("knowledge_assistant")

# 获取题目生成器系统消息
question_msg = prompt_templates.get_system_message("question_generator")
```

### 2. 题目生成提示词

支持选择题和判断题的提示词生成：

```python
# 生成选择题提示词
choice_prompt = prompt_templates.get_question_prompt(
    question_type="choice",
    count=3,
    difficulty="medium",
    content="学习内容文本"
)

# 生成判断题提示词
judgment_prompt = prompt_templates.get_question_prompt(
    question_type="judgment",
    count=2,
    difficulty="easy",
    content="学习内容文本"
)
```

### 3. 上下文消息构建

构建带有文档内容和选择文本的上下文消息：

```python
# 构建完整上下文
context = prompt_templates.build_context_content(
    base_content="基础系统消息",
    document_content="文档内容",
    selected_text="用户选择的文本"
)
```

## 模板类型

### 系统消息类型
- `knowledge_assistant` - 知识学习助手
- `question_generator` - 题目生成助手

### 题目类型
- `choice` - 选择题
- `judgment` - 判断题

## 使用示例

### 在 AI 服务中使用

```python
from app.services.prompt_templates import prompt_templates

class AIService:
    async def generate_questions(self, content: str, question_type: str = "choice"):
        # 使用模板生成提示词
        prompt = prompt_templates.get_question_prompt(
            question_type=question_type,
            count=3,
            difficulty="medium",
            content=content[:2000]
        )
        
        messages = [
            {"role": "system", "content": prompt_templates.get_system_message("question_generator")},
            {"role": "user", "content": prompt}
        ]
        
        return await self.chat_completion(messages)
```

## 扩展模板

### 添加新的系统消息

在 `PromptTemplates.SYSTEM_MESSAGES` 中添加新的消息类型：

```python
SYSTEM_MESSAGES = {
    "knowledge_assistant": "你是一个知识学习助手...",
    "question_generator": "你是一个专业的题目生成助手...",
    "new_assistant": "你是一个新的助手类型..."  # 新增
}
```

### 添加新的题目类型

在 `PromptTemplates.QUESTION_GENERATION` 中添加新的题目模板：

```python
QUESTION_GENERATION = {
    "choice_questions": "选择题模板...",
    "judgment_questions": "判断题模板...",
    "essay_questions": "论述题模板..."  # 新增
}
```

然后在 `get_question_prompt` 方法中添加对应的处理逻辑。

## 测试

运行测试文件验证模板功能：

```bash
cd knowledge_assistant/backend/app/services
python test_prompt_templates.py
```

## 优势

1. **集中管理** - 所有提示词集中在一个文件中，便于维护
2. **模板化** - 支持参数化模板，灵活生成不同内容
3. **类型安全** - 通过类方法提供类型检查和错误处理
4. **易于扩展** - 新增模板类型简单直观
5. **测试友好** - 提供完整的测试覆盖

## 注意事项

1. 修改模板后建议运行测试确保功能正常
2. 新增模板类型时需要同时更新相关的处理方法
3. 长文本内容会被自动截断以避免超出 API 限制
