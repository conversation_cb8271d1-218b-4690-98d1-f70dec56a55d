import os
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv

from app.core.database import engine, Base
from app.api import documents, chat, questions, proxy

# Import models to ensure they are registered with SQLAlchemy
from app.models import document, conversation, question

# Load environment variables
load_dotenv()

# Create database tables
Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title="Knowledge Assistant API",
    description="AI-powered knowledge learning assistant",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create upload directory
upload_dir = os.getenv("UPLOAD_DIR", "./uploads")
os.makedirs(upload_dir, exist_ok=True)

# Mount static files
app.mount("/uploads", StaticFiles(directory=upload_dir), name="uploads")

# Include API routes
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(questions.router, prefix="/api/questions", tags=["questions"])
app.include_router(proxy.router, prefix="/api", tags=["proxy"])

@app.get("/")
async def root():
    return {"message": "Knowledge Assistant API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    print("🚀 Starting Knowledge Assistant Backend...")
    print("📡 Server will be available at:")
    print("   - http://localhost:8000")
    print("   - http://127.0.0.1:8000")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("=" * 50)

    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
