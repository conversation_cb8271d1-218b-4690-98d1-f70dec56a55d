import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ChatMessage, Conversation, StreamResponse } from '@/types'
import { chatAPI } from '@/utils/api'

export const useChatStore = defineStore('chat', () => {
  // State
  const conversations = ref<Conversation[]>([])
  const currentConversation = ref<Conversation | null>(null)
  const messages = ref<ChatMessage[]>([])
  const loading = ref(false)
  const streaming = ref(false)
  const currentStreamingMessage = ref('')

  // Getters
  const hasConversations = computed(() => conversations.value.length > 0)
  const hasMessages = computed(() => messages.value.length > 0)
  const lastMessage = computed(() => messages.value[messages.value.length - 1])

  // Actions
  const fetchConversations = async () => {
    loading.value = true
    try {
      conversations.value = await chatAPI.listConversations()
    } catch (error) {
      console.error('Failed to fetch conversations:', error)
      // Don't throw error to avoid uncaught promise rejection
      conversations.value = []
    } finally {
      loading.value = false
    }
  }

  const selectConversation = async (conversationId: number) => {
    loading.value = true
    try {
      const conversation = conversations.value.find(c => c.id === conversationId)
      if (conversation) {
        currentConversation.value = conversation
        messages.value = await chatAPI.getConversationMessages(conversationId)
      }
    } catch (error) {
      console.error('Failed to select conversation:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const sendMessage = async (
    message: string,
    documentId?: number,
    selectedText?: string,
    useStreaming = true,
    options?: {
      mode?: string;
      thinking?: string;
      prompt?: string;
      activeContexts?: {
        docName?: boolean;
        docText?: boolean;
        selected?: boolean;
      };
    }
  ) => {
    // Add user message immediately
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    }
    messages.value.push(userMessage)

    const request = {
      message,
      conversation_id: currentConversation.value?.id,
      document_id: documentId,
      selected_text: selectedText,
      stream: useStreaming,
      mode: options?.mode,
      thinking: options?.thinking,
      prompt: options?.prompt,
      active_contexts: options?.activeContexts
    }

    if (useStreaming) {
      return await sendStreamingMessage(request)
    } else {
      return await sendNormalMessage(request)
    }
  }

  const sendStreamingMessage = async (request: any) => {
    streaming.value = true
    currentStreamingMessage.value = ''

    try {
      const stream = await chatAPI.streamMessage(request)
      const reader = stream.getReader()
      const decoder = new TextDecoder()

      // Add placeholder for AI message
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString()
      }
      messages.value.push(aiMessage)

      let conversationId: number | null = null

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6)) as StreamResponse
              
              if (data.type === 'conversation_id') {
                conversationId = data.data
                if (!currentConversation.value) {
                  // Create new conversation object
                  currentConversation.value = {
                    id: conversationId,
                    title: 'New Conversation',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  }
                  conversations.value.unshift(currentConversation.value)
                }
              } else if (data.type === 'content') {
                currentStreamingMessage.value += data.data
                // Update the last message (AI message)
                const lastMsg = messages.value[messages.value.length - 1]
                if (lastMsg.role === 'assistant') {
                  lastMsg.content = currentStreamingMessage.value
                }
              } else if (data.type === 'done') {
                break
              } else if (data.error) {
                throw new Error(data.error)
              }
            } catch (e) {
              // Ignore JSON parse errors for incomplete chunks
            }
          }
        }
      }

      return conversationId
    } catch (error) {
      console.error('Failed to send streaming message:', error)
      // Remove the placeholder AI message on error
      if (messages.value[messages.value.length - 1]?.role === 'assistant') {
        messages.value.pop()
      }
      throw error
    } finally {
      streaming.value = false
      currentStreamingMessage.value = ''
    }
  }

  const sendNormalMessage = async (request: any) => {
    loading.value = true
    try {
      const response = await chatAPI.sendMessage(request)
      
      // Add AI response
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: response.message,
        timestamp: new Date().toISOString()
      }
      messages.value.push(aiMessage)

      // Update current conversation
      if (!currentConversation.value && response.conversation_id) {
        currentConversation.value = {
          id: response.conversation_id,
          title: 'New Conversation',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        conversations.value.unshift(currentConversation.value)
      }

      return response.conversation_id
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteConversation = async (conversationId: number) => {
    try {
      await chatAPI.deleteConversation(conversationId)
      conversations.value = conversations.value.filter(c => c.id !== conversationId)
      
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
        messages.value = []
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error)
      throw error
    }
  }

  const clearMessages = () => {
    messages.value = []
    currentConversation.value = null
  }

  const startNewConversation = () => {
    currentConversation.value = null
    messages.value = []
  }

  return {
    // State
    conversations,
    currentConversation,
    messages,
    loading,
    streaming,
    currentStreamingMessage,
    
    // Getters
    hasConversations,
    hasMessages,
    lastMessage,
    
    // Actions
    fetchConversations,
    selectConversation,
    sendMessage,
    deleteConversation,
    clearMessages,
    startNewConversation
  }
})
