import json
import asyncio
from typing import List, Dict, Any, AsyncGenerator
import httpx
from app.core.config import settings
from app.services.prompt_templates import prompt_templates

class AIService:
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def chat_completion_stream(
        self,
        messages: List[Dict[str, str]]
    ) -> AsyncGenerator[str, None]:
        """Send streaming chat completion request to DeepSeek API"""

        payload = {
            "model": "deepseek-chat",
            "messages": messages,
            "temperature": settings.TEMPERATURE,
            "max_tokens": settings.MAX_TOKENS,
            "stream": True
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload
            ) as response:
                if response.status_code != 200:
                    yield f"Error: {response.status_code} - {response.text}"
                    return

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix
                        if data == "[DONE]":
                            break
                        try:
                            chunk = json.loads(data)
                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                delta = chunk["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue

    async def chat_completion(
        self,
        messages: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """Send non-streaming chat completion request to DeepSeek API"""

        payload = {
            "model": "deepseek-chat",
            "messages": messages,
            "temperature": settings.TEMPERATURE,
            "max_tokens": settings.MAX_TOKENS,
            "stream": False
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API Error: {response.status_code} - {response.text}"}
    
    async def generate_questions(
        self,
        content: str,
        question_type: str = "choice",
        difficulty: str = "medium",
        count: int = 3
    ) -> List[Dict[str, Any]]:
        """Generate questions based on content"""

        # 使用提示词模板生成提示词
        prompt = prompt_templates.get_question_prompt(
            question_type=question_type,
            count=count,
            difficulty=difficulty,
            content=content[:2000]  # Limit content length
        )

        messages = [
            {"role": "system", "content": prompt_templates.get_system_message("question_generator")},
            {"role": "user", "content": prompt}
        ]
        
        try:
            response = await self.chat_completion(messages)
            if "error" in response:
                return []
            
            content = response["choices"][0]["message"]["content"]
            
            # Try to parse JSON
            try:
                questions = json.loads(content)
                return questions if isinstance(questions, list) else []
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from text
                import re
                json_match = re.search(r'\[.*\]', content, re.DOTALL)
                if json_match:
                    try:
                        questions = json.loads(json_match.group())
                        return questions if isinstance(questions, list) else []
                    except json.JSONDecodeError:
                        pass
                return []
                
        except Exception as e:
            print(f"Error generating questions: {e}")
            return []
    
    def prepare_context_messages(
        self,
        user_message: str,
        document_content: str = "",
        selected_text: str = "",
        chat_history: List[Dict[str, str]] = None
    ) -> List[Dict[str, str]]:
        """Prepare messages with context for chat completion"""

        messages = []

        # 使用提示词模板构建系统消息
        base_system_content = prompt_templates.get_system_message("knowledge_assistant")
        system_content = prompt_templates.build_context_content(
            base_content=base_system_content,
            document_content=document_content,
            selected_text=selected_text
        )

        messages.append({"role": "system", "content": system_content})
        
        # Add chat history (last few messages)
        if chat_history:
            recent_history = chat_history[-10:]  # Keep last 10 messages
            messages.extend(recent_history)
        
        # Add current user message
        messages.append({"role": "user", "content": user_message})
        
        return messages

# Global AI service instance
ai_service = AIService()
