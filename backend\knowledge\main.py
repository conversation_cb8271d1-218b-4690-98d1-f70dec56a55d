import re
import httpx
import json
import sys
from pathlib import Path
# 将上级目录加入Python路径
parent_dir = str(Path(__file__).parent.parent)  # 获取上两级目录
sys.path.append(parent_dir) 
from langchain_openai.chat_models import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.output_parsers import ResponseSchema
from langchain.output_parsers import StructuredOutputParser,PydanticOutputParser
import logging
import re
from util import extract_text_blocks,json_to_markdown, save_to_json_append,txt_to_json_str,append_json_to_txt,save_to_json
from pydantic import BaseModel, Field, validator
from typing import List,Dict
from deepseek_free_api import  ChatDeepSeek
from template import fix_template,fix_template_version0,fix_template_version1
from template_question import ProcessErrorCorrectionQuestions, TrueFalseQuestions, question_template,ChoiceQuestion,ChoiceQuestions,process_error_question_template
import hashlib

class FixTxtBody(BaseModel):
    fix_txt: str = Field(description="AI修正后的文本")
    fix_dict: Dict = Field(description="AI修正过程中修复的短语或词语,为dict格式列表,如{'离开端': '离开段', '四级别': '次级别', key和value分别不应超过8个字 }")


# 配置日志格式和级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
chat = ChatOpenAI(
    temperature=0.7,
    model="deepseek-chat",
    # model="deepseek-reasoner",
    openai_api_base="https://api.deepseek.com",  # ✅ 使用 `openai_api_base` 而非 `base_url`
    openai_api_key="sk-5cdc326fd5914802825268047e9c6e2f",  # 替换为你的 API Key
    http_client=httpx.Client(verify=False)  # ⚠️ 禁用 SSL 验证（不安全，仅测试用）
)
# invoke
def knowledge_extraction(source_txt, template):

    fix_dict= txt_to_json_str(input_file='dict_prd.txt')

    output_parser = PydanticOutputParser(pydantic_object=FixTxtBody)
        
    format_instructions = output_parser.get_format_instructions()
 
    prompt = ChatPromptTemplate.from_template(template=template)
    messages = prompt.format_messages(text=source_txt, fix_dict=fix_dict, format_instructions=format_instructions)
    response = chat(messages)
    logging.info("结果: %s", response.content)
    fix_body = output_parser.parse(response.content)
    logging.info("修正后的文本: %s", fix_body.fix_txt)
    logging.info("修正后的文本: %s", fix_body.fix_dict)
    append_json_to_txt(fix_body.fix_dict)
    return fix_body.fix_txt 



def anwer(path):
    pass 

def exam(topic,knowledge,question_template,question_type,type=ChoiceQuestions):
    
    hashkey = hashlib.md5(knowledge.encode('utf-8')).hexdigest()
    output_parser = PydanticOutputParser(pydantic_object=type)
        
    format_instructions = output_parser.get_format_instructions()
 
    prompt = ChatPromptTemplate.from_template(template=question_template)
    messages = prompt.format_messages(knowledge=knowledge, topic=topic,question_type=question_type, format_instructions=format_instructions)
    response = chat(messages)
    logging.info("结果: %s", response.content)
    # fix_body = output_parser.parse(response.content)
    # logging.info("修正后的文本: %s", fix_body.fix_txt)
    # logging.info("修正后的文本: %s", fix_body.fix_dict)
    # print(type(response.content))
    # print(type(output_parser.parse(response.content)))
    # print(output_parser.parse(response.content))
    choice_questions = output_parser.parse(response.content)
    choice_questions_json = choice_questions.model_dump() 
    for question in choice_questions_json['qustions']:
        question['hashkey'] = hashkey
    return choice_questions_json



# 文本 转 markdown 知识点 
def get_choice_questions():

    topic ='Transformer 注意力机制'
    knowledge ="""## 2. 关键组件
### 2.1 多头注意力（Multi-Head Attention）
- 并行多个注意力头，捕获不同子空间的特征
- 计算流程：
  1. 线性投影生成多组Q/K/V
  2. 分别计算缩放点积注意力
  3. 拼接结果并通过线性层融合"""
    question_template = question_template
    question_type = '选择题'
    choice_questions_json = exam(topic,knowledge,question_template,question_type)
    save_to_json_append(choice_questions_json, "questions_1.json")
    print(choice_questions_json)
    

def get_truefalse_questions():
    topic ='Transformer 注意力机制'
    knowledge ="""## 2. 关键组件
### 2.1 多头注意力（Multi-Head Attention）
- 并行多个注意力头，捕获不同子空间的特征
- 计算流程：
  1. 线性投影生成多组Q/K/V
  2. 分别计算缩放点积注意力
  3. 拼接结果并通过线性层融合"""
    question_template = question_template
    question_type = '判断题'
    truefalse_questions_json = exam(topic,knowledge,question_template,question_type,TrueFalseQuestions)
    save_to_json_append(truefalse_questions_json, "truefalse_questions_1.json")
    print(truefalse_questions_json)

if __name__ == "__main__":  
    topic ='Transformer 注意力机制'
    knowledge ="""## 2. 关键组件
### 2.1 多头注意力（Multi-Head Attention）
- 并行多个注意力头，捕获不同子空间的特征
- 计算流程：
  1. 线性投影生成多组Q/K/V
  2. 分别计算缩放点积注意力
  3. 拼接结果并通过线性层融合"""
    question_template = process_error_question_template
    question_type = '流程纠错题'
    process_error_question = exam(topic,knowledge,question_template,question_type,ProcessErrorCorrectionQuestions)
    save_to_json_append(process_error_question, "process_error_question_1.json")
    print(process_error_question)
    
# response = chat('将Transformer 注意力机制 涉及的内容转换成知识点，并以markdown格式输出')
# logging.info("结果: %s", response.content)


# 将以下 文本，总结成知识点，并以markdown 文本输出
# 以下是transformer 多头注意力的其中的一个知识点，请开启解答模式，针对这个知识点，提出若干问题，并依次给出解答，以markdown 格式输出，问题，和解答。
# 以下是transformer 多头注意力的其中的一个知识点，请开启脑图考试模式，针知识点，提出30问题，问题类型包含，选择题 10个，判断题10个，多选题10ge，难易程度为 简单：中等：困难6:3:1：## 1. 核心概念
# - **自注意力（Self-Attention）**
#   输入序列内部元素间的交互机制，计算每个元素与其他元素的关联权重。
# - **缩放点积注意力（Scaled Dot-Product Attention）**
#   通过查询（Q）、键（K）、值（V）矩阵计算注意力权重，公式：
#   $\text{Attention}(Q,K,V)=\text{softmax}(\frac{QK^T}{\sqrt{d_k}})V$