{"qustions": [{"qustion": "多头注意力机制中，每个注意力头的Q/K/V是通过什么操作得到的？", "choice": {"A": "随机初始化", "B": "线性投影", "C": "卷积操作", "D": "池化操作"}, "answer": "B", "level": "简单"}, {"qustion": "在多头注意力中，缩放点积注意力的计算公式是？", "choice": {"A": "$\\text{Attention}(Q, K, V) = \\text{softmax}(\\frac{QK^T}{\\sqrt{d_k}})V$", "B": "$\\text{Attention}(Q, K, V) = \\text{softmax}(QK^T)V$", "C": "$\\text{Attention}(Q, K, V) = \\text{tanh}(\\frac{QK^T}{\\sqrt{d_k}})V$", "D": "$\\text{Attention}(Q, K, V) = \\text{ReLU}(\\frac{QK^T}{\\sqrt{d_k}})V$"}, "answer": "A", "level": "简单"}, {"qustion": "多头注意力机制中，多个注意力头的输出是如何融合的？", "choice": {"A": "直接相加", "B": "拼接后通过线性层", "C": "取最大值", "D": "取平均值"}, "answer": "B", "level": "简单"}, {"qustion": "多头注意力机制中，通常使用多少个注意力头？", "choice": {"A": "1个", "B": "2个", "C": "8个", "D": "根据模型需求设定"}, "answer": "D", "level": "简单"}, {"qustion": "多头注意力机制的主要优势是什么？", "choice": {"A": "减少计算量", "B": "捕获不同子空间的特征", "C": "提高模型训练速度", "D": "减少参数数量"}, "answer": "B", "level": "简单"}, {"qustion": "在多头注意力中，缩放因子$\\sqrt{d_k}$的作用是什么？", "choice": {"A": "增加梯度", "B": "防止点积过大导致softmax梯度消失", "C": "减少计算量", "D": "增加模型复杂度"}, "answer": "B", "level": "中等"}, {"qustion": "多头注意力机制中，每个注意力头的维度通常是总维度的多少？", "choice": {"A": "相等", "B": "1/头数", "C": "头数的平方", "D": "随机"}, "answer": "B", "level": "中等"}, {"qustion": "以下哪项不是多头注意力的计算步骤？", "choice": {"A": "线性投影生成多组Q/K/V", "B": "分别计算缩放点积注意力", "C": "拼接结果并通过线性层融合", "D": "使用卷积核进行特征提取"}, "answer": "D", "level": "中等"}, {"qustion": "假设总维度为512，使用8个头，每个头的维度是多少？", "choice": {"A": "512", "B": "64", "C": "128", "D": "256"}, "answer": "B", "level": "困难"}, {"qustion": "多头注意力机制中，为什么需要多个注意力头？", "choice": {"A": "为了增加模型参数", "B": "为了并行计算提高效率", "C": "为了捕获输入序列中不同位置的不同关系", "D": "为了减少计算量"}, "answer": "C", "level": "困难"}]}