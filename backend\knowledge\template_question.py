
from typing import List,Dict
from pydantic import BaseModel, Field, validator

#选择题
class ChoiceQuestion(BaseModel):
    qustion: str = Field(description="问题描述")
    choice: Dict = Field(description="问题选项 ，如 {'A': '12.5','B': '0','C': '-1','C': '25'")
    answer: str = Field(description="问题答案,问题选项中的一项，如 A")
    level: str = Field(description="问题难易程度,简单，中等，困难其中一项")
    explanation: str = Field(description="问题解析,对正确答案进行解析")
class ChoiceQuestions(BaseModel):
    qustions: List[ChoiceQuestion] = Field(description="问题列表")
    
#判断题
class TrueFalseQuestion(BaseModel):
    qustion: str = Field(description="问题描述")
    answer: str = Field(description="问题答案,正确,错误")
    level: str = Field(description="问题难易程度,简单，中等，困难其中一项")
    explanation: str = Field(description="问题解析,对正确答案进行解析")
class TrueFalseQuestions(BaseModel):
    qustions: List[TrueFalseQuestion] = Field(description="问题列表")

class ProcessErrorCorrectionQuestion(BaseModel):
    qustion: str = Field(description="问题描述")
    answer: str = Field(description="问题答案,正确,错误")
    incorrect_mermaid:str = Field(description="错误的mermaid流程图")
    correct_mermaid:str = Field(description="正确的mermaid流程图")
    level: str = Field(description="问题难易程度,简单,中等,困难其中一项")
    explanation: str = Field(description="问题解析,对错误的流程图错读点进行纠错,给出正确解析")
class ProcessErrorCorrectionQuestions(BaseModel):
    qustions: List[ProcessErrorCorrectionQuestion] = Field(description="问题列表")
    
question_template = """\
以下关于{topic}的其中的一个知识点，请开启考试模式，针对知识点，提出10个问题，问题类型为{question_type},难易程度为简单：中等：困难6:3:1。问题和答案以json格式输出。
问题中涉及的公式需要以markdown的公式格式输出
    知识点：{knowledge}
 {format_instructions}
"""

#ProcessError Correction Questions 

process_error_question_template = """\
以下关于{topic}的其中的一个知识点，请开启考试模式，针对知识点，提出3个问题，问题类型为{question_type},难易程度为简单：中等：困难1:3:3。问题和答案以json格式输出。
问题中涉及的公式需要以markdown的公式格式输出,流程图以mermaid语法描述。
示例：以下为某某问题的的错误流程图，请找出并修正至少2处错误： 
```mermaid
graph TD
    A[输入X] --> B[线性投影Q]
    A --> C[线性投影K] 
    A --> D[线性投影V]
    B --> E[计算QKᵀ]
    C --> E
    E --> F[softmax]
    F --> G[乘积运算]
    D --> H[输出拼接]
    G --> H
    H --> I[线性投影]
```
**错误点提示**：
1. 某某环节问题
2. 某某关键步骤缺失
    知识点：{knowledge}
 {format_instructions}
"""