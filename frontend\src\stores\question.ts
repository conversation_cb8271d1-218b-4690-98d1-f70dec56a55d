import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Question, QuestionWithAnswer, AnswerResponse } from '@/types'
import { questionsAPI } from '@/utils/api'

export const useQuestionStore = defineStore('question', () => {
  // State
  const questions = ref<Question[]>([])
  const currentQuestion = ref<Question | null>(null)
  const answeredQuestions = ref<Set<number>>(new Set())
  const correctAnswers = ref<Set<number>>(new Set())
  const loading = ref(false)
  const generating = ref(false)
  const stats = ref({
    total_questions: 0,
    total_answered: 0,
    correct_answers: 0,
    accuracy_rate: 0
  })

  // Getters
  const hasQuestions = computed(() => questions.value.length > 0)
  const unansweredQuestions = computed(() => 
    questions.value.filter(q => !answeredQuestions.value.has(q.id))
  )
  const nextQuestion = computed(() => unansweredQuestions.value[0] || null)
  const accuracyRate = computed(() => {
    const answered = answeredQuestions.value.size
    const correct = correctAnswers.value.size
    return answered > 0 ? Math.round((correct / answered) * 100) : 0
  })

  // Actions
  const fetchQuestions = async (params: {
    document_id?: number
    question_type?: string
    difficulty?: string
    skip?: number
    limit?: number
  } = {}) => {
    loading.value = true
    try {
      questions.value = await questionsAPI.listQuestions(params)
    } catch (error) {
      console.error('Failed to fetch questions:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const generateQuestions = async (
    documentId?: number,
    content?: string,
    questionType: 'choice' | 'judgment' = 'choice',
    difficulty: 'easy' | 'medium' | 'hard' = 'medium',
    count = 3
  ) => {
    generating.value = true
    try {
      const response = await questionsAPI.generateQuestions({
        document_id: documentId,
        content,
        question_type: questionType,
        difficulty,
        count
      })

      // Add new questions to the beginning
      questions.value.unshift(...response.saved_questions)
      return response.saved_questions
    } catch (error) {
      console.error('Failed to generate questions:', error)
      throw error
    } finally {
      generating.value = false
    }
  }

  const submitAnswer = async (questionId: number, userAnswer: string, timeSpent?: number) => {
    try {
      console.log('📤 questionStore.submitAnswer 调用API:', {
        question_id: questionId,
        user_answer: userAnswer,
        time_spent: timeSpent
      })

      const response = await questionsAPI.submitAnswer({
        question_id: questionId,
        user_answer: userAnswer,
        time_spent: timeSpent
      })

      console.log('📥 questionStore.submitAnswer API响应:', response)

      // Mark question as answered
      answeredQuestions.value.add(questionId)

      // Track correct answers
      if (response.is_correct) {
        correctAnswers.value.add(questionId)
      }

      return response
    } catch (error) {
      console.error('❌ Failed to submit answer:', error)
      throw error
    }
  }

  const fetchStats = async () => {
    try {
      stats.value = await questionsAPI.getStats()
    } catch (error) {
      console.error('Failed to fetch stats:', error)
      // Don't throw error to avoid uncaught promise rejection
      stats.value = {
        total_questions: 0,
        total_answered: 0,
        correct_answers: 0,
        accuracy_rate: 0
      }
    }
  }

  const selectQuestion = (question: Question) => {
    currentQuestion.value = question
  }

  const removeQuestion = (questionId: number) => {
    questions.value = questions.value.filter(q => q.id !== questionId)
    answeredQuestions.value.delete(questionId)
    correctAnswers.value.delete(questionId)
    
    if (currentQuestion.value?.id === questionId) {
      currentQuestion.value = null
    }
  }

  const clearQuestions = () => {
    questions.value = []
    currentQuestion.value = null
    answeredQuestions.value.clear()
    correctAnswers.value.clear()
  }

  const resetAnswers = () => {
    answeredQuestions.value.clear()
    correctAnswers.value.clear()
  }

  const isQuestionAnswered = (questionId: number) => {
    return answeredQuestions.value.has(questionId)
  }

  const isAnswerCorrect = (questionId: number) => {
    return correctAnswers.value.has(questionId)
  }

  const getQuestionsByDifficulty = (difficulty: 'easy' | 'medium' | 'hard') => {
    return questions.value.filter(q => q.difficulty === difficulty)
  }

  const getQuestionsByType = (type: 'choice' | 'judgment') => {
    return questions.value.filter(q => q.question_type === type)
  }

  return {
    // State
    questions,
    currentQuestion,
    answeredQuestions,
    correctAnswers,
    loading,
    generating,
    stats,
    
    // Getters
    hasQuestions,
    unansweredQuestions,
    nextQuestion,
    accuracyRate,
    
    // Actions
    fetchQuestions,
    generateQuestions,
    submitAnswer,
    fetchStats,
    selectQuestion,
    removeQuestion,
    clearQuestions,
    resetAnswers,
    isQuestionAnswered,
    isAnswerCorrect,
    getQuestionsByDifficulty,
    getQuestionsByType
  }
})
