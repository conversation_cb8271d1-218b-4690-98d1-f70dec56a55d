# Dependencies
node_modules/
*/node_modules/
frontend/node_modules/
package-lock.json
frontend/package-lock.json

# Build outputs
dist/
build/
*.tsbuildinfo
frontend/dist/

# Environment files
.env
.env.local
.env.*.local
backend/.env

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
knowledge_assistant.db

# Python
__pycache__/
*/__pycache__/
**/__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
backend/__pycache__/
backend/app/__pycache__/
backend/app/*/__pycache__/

# Uploads
uploads/
backend/uploads/

# Cache
.cache/
.parcel-cache/

# Coverage
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
backend/__pycache__/main.cpython-310.pyc
backend/app/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/chat.cpython-310.pyc
backend/app/api/__pycache__/documents.cpython-310.pyc
backend/app/api/__pycache__/questions.cpython-310.pyc
backend/app/core/__pycache__/__init__.cpython-310.pyc
backend/app/core/__pycache__/config.cpython-310.pyc
backend/app/core/__pycache__/database.cpython-310.pyc
backend/app/models/__pycache__/__init__.cpython-310.pyc
backend/app/models/__pycache__/conversation.cpython-310.pyc
backend/app/models/__pycache__/document.cpython-310.pyc
backend/app/models/__pycache__/question.cpython-310.pyc
backend/app/services/__pycache__/__init__.cpython-310.pyc
backend/app/services/__pycache__/ai_service.cpython-310.pyc
backend/app/services/__pycache__/document_service.cpython-310.pyc
frontend/node_modules/.package-lock.json
frontend/package-lock.json
backend/__pycache__/main.cpython-310.pyc
backend/app/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/chat.cpython-310.pyc
backend/app/api/__pycache__/documents.cpython-310.pyc
backend/app/api/__pycache__/questions.cpython-310.pyc
backend/app/core/__pycache__/__init__.cpython-310.pyc
backend/app/core/__pycache__/config.cpython-310.pyc
backend/app/core/__pycache__/database.cpython-310.pyc
backend/app/models/__pycache__/__init__.cpython-310.pyc
backend/app/models/__pycache__/conversation.cpython-310.pyc
backend/app/models/__pycache__/document.cpython-310.pyc
backend/app/models/__pycache__/question.cpython-310.pyc
backend/app/services/__pycache__/__init__.cpython-310.pyc
backend/app/services/__pycache__/ai_service.cpython-310.pyc
backend/app/services/__pycache__/document_service.cpython-310.pyc
backend/knowledge_assistant.db
frontend/node_modules/.package-lock.json
frontend/package-lock.json
backend/__pycache__/main.cpython-310.pyc
backend/app/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/__init__.cpython-310.pyc
backend/app/api/__pycache__/chat.cpython-310.pyc
backend/app/api/__pycache__/documents.cpython-310.pyc
backend/app/api/__pycache__/questions.cpython-310.pyc
backend/app/core/__pycache__/__init__.cpython-310.pyc
backend/app/core/__pycache__/config.cpython-310.pyc
backend/app/core/__pycache__/database.cpython-310.pyc
backend/app/models/__pycache__/__init__.cpython-310.pyc
backend/app/models/__pycache__/conversation.cpython-310.pyc
backend/app/models/__pycache__/document.cpython-310.pyc
backend/app/models/__pycache__/question.cpython-310.pyc
backend/app/services/__pycache__/__init__.cpython-310.pyc
backend/app/services/__pycache__/ai_service.cpython-310.pyc
backend/app/services/__pycache__/document_service.cpython-310.pyc
backend/knowledge_assistant.db
frontend/node_modules/.package-locky.json
frontend/package-lock.json
/frontend/node_modules
/frontend/node_modules
/frontend/node_modules
*.pyc
/frontend/node_modules
