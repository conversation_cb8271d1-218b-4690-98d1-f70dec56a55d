from typing import List, Optional
from fastapi import APIRout<PERSON>, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
import json

from app.core.database import get_db
from app.models.conversation import Conversation, Message
from app.models.document import Document
from app.services.ai_service import ai_service

router = APIRouter()

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[int] = None
    document_id: Optional[int] = None
    selected_text: Optional[str] = ""
    stream: bool = True

class ChatResponse(BaseModel):
    message: str
    conversation_id: int
    message_id: int

class ConversationResponse(BaseModel):
    id: int
    title: str
    created_at: str
    updated_at: str

@router.post("/send", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    db: Session = Depends(get_db)
):
    """Send a message and get AI response (non-streaming)"""
    
    try:
        # Get or create conversation
        if request.conversation_id:
            conversation = db.query(Conversation).filter(
                Conversation.id == request.conversation_id
            ).first()
            if not conversation:
                raise HTTPException(status_code=404, detail="Conversation not found")
        else:
            conversation = Conversation(title="New Conversation")
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
        
        # Get document content if provided
        document_content = ""
        if request.document_id:
            document = db.query(Document).filter(Document.id == request.document_id).first()
            if document and document.content:
                document_content = document.content
        
        # Get chat history
        chat_history = []
        messages = db.query(Message).filter(
            Message.conversation_id == conversation.id
        ).order_by(Message.created_at.desc()).limit(10).all()
        
        for msg in reversed(messages):
            chat_history.append({
                "role": msg.role,
                "content": msg.content
            })
        
        # Prepare messages for AI
        ai_messages = ai_service.prepare_context_messages(
            user_message=request.message,
            document_content=document_content,
            selected_text=request.selected_text or "",
            chat_history=chat_history
        )
        
        # Save user message
        user_message = Message(
            conversation_id=conversation.id,
            role="user",
            content=request.message
        )
        db.add(user_message)
        db.commit()
        
        # Get AI response
        ai_response = await ai_service.chat_completion(ai_messages)
        
        if "error" in ai_response:
            raise HTTPException(status_code=500, detail=ai_response["error"])
        
        ai_content = ai_response["choices"][0]["message"]["content"]
        
        # Save AI message
        ai_message = Message(
            conversation_id=conversation.id,
            role="assistant",
            content=ai_content
        )
        db.add(ai_message)
        db.commit()
        db.refresh(ai_message)
        
        return ChatResponse(
            message=ai_content,
            conversation_id=conversation.id,
            message_id=ai_message.id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")

@router.api_route("/stream", methods=["GET", "POST"])
async def stream_message(
    request: ChatRequest,
    db: Session = Depends(get_db)
):
    """Send a message and get streaming AI response"""
    
    async def generate_response():
        try:
            # Get or create conversation
            if request.conversation_id:
                conversation = db.query(Conversation).filter(
                    Conversation.id == request.conversation_id
                ).first()
                if not conversation:
                    yield f"data: {json.dumps({'error': 'Conversation not found'})}\n\n"
                    return
            else:
                conversation = Conversation(title="New Conversation")
                db.add(conversation)
                db.commit()
                db.refresh(conversation)
            
            # Get document content if provided
            document_content = ""
            if request.document_id:
                document = db.query(Document).filter(Document.id == request.document_id).first()
                if document and document.content:
                    document_content = document.content
            
            # Get chat history
            chat_history = []
            messages = db.query(Message).filter(
                Message.conversation_id == conversation.id
            ).order_by(Message.created_at.desc()).limit(10).all()
            
            for msg in reversed(messages):
                chat_history.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # Prepare messages for AI
            ai_messages = ai_service.prepare_context_messages(
                user_message=request.message,
                document_content=document_content,
                selected_text=request.selected_text or "",
                chat_history=chat_history
            )
            
            # Save user message
            user_message = Message(
                conversation_id=conversation.id,
                role="user",
                content=request.message
            )
            db.add(user_message)
            db.commit()
            
            # Send conversation info first
            yield f"data: {json.dumps({'type': 'conversation_id', 'data': conversation.id})}\n\n"
            
            # Stream AI response
            full_response = ""
            async for chunk in ai_service.chat_completion_stream(ai_messages):
                if chunk:
                    full_response += chunk
                    yield f"data: {json.dumps({'type': 'content', 'data': chunk})}\n\n"
            
            # Save AI message
            ai_message = Message(
                conversation_id=conversation.id,
                role="assistant",
                content=full_response
            )
            db.add(ai_message)
            db.commit()
            
            yield f"data: {json.dumps({'type': 'done', 'data': 'Stream completed'})}\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@router.get("/conversations", response_model=List[ConversationResponse])
async def list_conversations(
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """List all conversations"""
    
    conversations = db.query(Conversation).order_by(
        Conversation.updated_at.desc()
    ).offset(skip).limit(limit).all()
    
    return [
        ConversationResponse(
            id=conv.id,
            title=conv.title,
            created_at=conv.created_at.isoformat(),
            updated_at=conv.updated_at.isoformat() if conv.updated_at else conv.created_at.isoformat()
        )
        for conv in conversations
    ]

@router.get("/conversations/{conversation_id}/messages", response_model=List[ChatMessage])
async def get_conversation_messages(
    conversation_id: int,
    db: Session = Depends(get_db)
):
    """Get messages for a conversation"""
    
    conversation = db.query(Conversation).filter(
        Conversation.id == conversation_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    messages = db.query(Message).filter(
        Message.conversation_id == conversation_id
    ).order_by(Message.created_at.asc()).all()
    
    return [
        ChatMessage(role=msg.role, content=msg.content)
        for msg in messages
    ]

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    db: Session = Depends(get_db)
):
    """Delete a conversation and all its messages"""
    
    conversation = db.query(Conversation).filter(
        Conversation.id == conversation_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    db.delete(conversation)
    db.commit()
    
    return {"message": "Conversation deleted successfully"}
