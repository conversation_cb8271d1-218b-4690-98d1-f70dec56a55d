import sqlite3
import os
from typing import List

class QuestionExporter:
    """
    工具类：将questions表导出为markdown文件，格式参考output_1.md
    """
    def __init__(self, db_path: str, output_path: str):
        self.db_path = db_path
        self.output_path = output_path

    def fetch_questions(self) -> List[dict]:
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM questions WHERE is_active=1 ORDER BY id ASC")
        rows = cursor.fetchall()
        conn.close()
        return [dict(row) for row in rows]

    def parse_options(self, options_raw) -> List[str]:
        # 假设options字段为json字符串或逗号分隔
        import json
        if not options_raw:
            return []
        try:
            result = json.loads(options_raw)
            if isinstance(result, list):
                return result
            elif isinstance(result, str):
                return [result]
            else:
                return []
        except Exception:
            if isinstance(options_raw, str):
                return [opt.strip() for opt in options_raw.split(',') if opt.strip()]
            return []

    def export_to_markdown(self):
        questions = self.fetch_questions()
        lines = ["# 题目集\n"]
        for idx, q in enumerate(questions, 1):
            lines.append(f"## 题目 {idx}（难度：{q.get('difficulty', '未知')}）")
            lines.append(q.get('question_text', ''))
            lines.append("\n### 选项")
            options = self.parse_options(q.get('options'))
            for i, opt in enumerate(options):
                label = chr(ord('A') + i)
                lines.append(f"- **{label}**: {opt}")
            lines.append("\n### 答案")
            correct = q.get('correct_answer', '')
            if correct:
                lines.append(f"**正确答案**: {correct}")
            else:
                lines.append("**正确答案**: ")
            lines.append("\n### 解析")
            lines.append(q.get('explanation', ''))
            lines.append("\n---\n")
        with open(self.output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        print(f"导出完成: {self.output_path}")

if __name__ == "__main__":
    db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'knowledge_assistant.db'))
    output_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'questions_export.md'))
    exporter = QuestionExporter(db_path, output_path)
    exporter.export_to_markdown()
