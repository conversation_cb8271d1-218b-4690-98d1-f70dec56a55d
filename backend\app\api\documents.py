import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse, FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.core.config import settings
from app.models.document import Document
from app.services.document_service import DocumentService

router = APIRouter()


def safe_filename_for_header(filename: str) -> str:
    """
    生成安全的文件名用于HTTP头
    """
    import urllib.parse
    import re

    # 移除或替换不安全的字符
    safe_filename = re.sub(r'[^\w\s\-_\.]', '', filename)

    # 如果文件名为空或只包含特殊字符，使用默认名称
    if not safe_filename.strip():
        safe_filename = "document"

    try:
        # 尝试ASCII编码
        safe_filename.encode('ascii')
        return safe_filename
    except UnicodeEncodeError:
        # 如果包含非ASCII字符，使用URL编码
        return urllib.parse.quote(safe_filename, safe='')

class DocumentResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_type: str
    content: str = ""
    file_size: int = 0
    is_processed: bool = False
    created_at: str

class URLUploadRequest(BaseModel):
    url: str
    title: str = ""

@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload and process a document file"""
    
    # Validate file type
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File type {file_extension} not allowed. Allowed types: {settings.ALLOWED_EXTENSIONS}"
        )
    
    # Validate file size
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
        )
    
    # Reset file pointer
    await file.seek(0)
    
    try:
        # Save file to disk
        file_path = await DocumentService.save_uploaded_file(file)
        
        # Create document record
        document = Document(
            filename=os.path.basename(file_path),
            original_filename=file.filename,
            file_type=file_extension[1:],  # Remove the dot
            file_path=file_path,
            file_size=len(file_content)
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Process document to extract text
        document = await DocumentService.process_document(db, document)
        
        return DocumentResponse(
            id=document.id,
            filename=document.filename,
            original_filename=document.original_filename,
            file_type=document.file_type,
            content=document.content or "",
            file_size=document.file_size or 0,
            is_processed=document.is_processed,
            created_at=document.created_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@router.post("/upload-url", response_model=DocumentResponse)
async def upload_url(
    request: URLUploadRequest,
    db: Session = Depends(get_db)
):
    """Upload and process a web page URL"""
    
    try:
        # Create document record for URL
        document = Document(
            filename=request.title or "Web Page",
            original_filename=request.url,
            file_type="url",
            url=request.url
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Process URL to extract text
        document = await DocumentService.process_document(db, document)
        
        return DocumentResponse(
            id=document.id,
            filename=document.filename,
            original_filename=document.original_filename,
            file_type=document.file_type,
            content=document.content or "",
            file_size=len(document.content or ""),
            is_processed=document.is_processed,
            created_at=document.created_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing URL: {str(e)}")

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Get document by ID"""
    
    document = DocumentService.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return DocumentResponse(
        id=document.id,
        filename=document.filename,
        original_filename=document.original_filename,
        file_type=document.file_type,
        content=document.content or "",
        file_size=document.file_size or 0,
        is_processed=document.is_processed,
        created_at=document.created_at.isoformat()
    )

@router.get("/", response_model=List[DocumentResponse])
async def list_documents(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List all documents"""
    
    documents = db.query(Document).offset(skip).limit(limit).all()
    
    return [
        DocumentResponse(
            id=doc.id,
            filename=doc.filename,
            original_filename=doc.original_filename,
            file_type=doc.file_type,
            content=doc.content or "",
            file_size=doc.file_size or 0,
            is_processed=doc.is_processed,
            created_at=doc.created_at.isoformat()
        )
        for doc in documents
    ]


@router.get("/{document_id}/file")
async def get_document_file(
    document_id: int,
    db: Session = Depends(get_db)
):
    """获取文档文件"""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # 构建文件路径
    file_path = os.path.join(settings.UPLOAD_DIR, document.filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    # 返回文件
    media_type = 'application/pdf' if document.file_type == 'pdf' else 'application/octet-stream'

    # 对于PDF文件，设置inline显示
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': '*'
    }

    if document.file_type == 'pdf':
        # 使用安全的文件名处理
        safe_name = safe_filename_for_header(document.original_filename)
        headers['Content-Disposition'] = f'inline; filename="{safe_name}"'

    return FileResponse(
        path=file_path,
        filename=document.original_filename,
        media_type=media_type,
        headers=headers
    )

@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Delete document"""
    
    success = DocumentService.delete_document(db, document_id)
    if not success:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return {"message": "Document deleted successfully"}
