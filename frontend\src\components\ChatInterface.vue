<template>
  <div class="chat-interface vertical-layout">
    <!-- Conversation List (Sidebar) -->
    <el-drawer v-model="showConversations" title="对话历史" direction="ltr" size="300px">
      <div class="conversation-list">
        <div
          v-for="conversation in chatStore.conversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ active: chatStore.currentConversation?.id === conversation.id }"
          @click="selectConversation(conversation.id)"
        >
          <div class="conversation-info">
            <div class="conversation-title">{{ conversation.title }}</div>
            <div class="conversation-time">{{ formatTime(conversation.updated_at) }}</div>
          </div>
          <el-button link size="small" @click.stop="deleteConversation(conversation.id)">
            <el-icon><delete /></el-icon>
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 主体区域：消息区在上，输入区在下 -->
    <div class="main-content-vertical">
      <div class="messages-container">
        <el-scrollbar ref="scrollbarRef" class="messages-scroll">
          <div class="messages-list">
            <div v-if="!chatStore.hasMessages" class="empty-messages">
              <el-empty description="开始新的对话吧！">
                <template #image>
                  <el-icon size="60"><chat-dot-round /></el-icon>
                </template>
              </el-empty>
            </div>

            <div
              v-for="(message, index) in chatStore.messages"
              :key="index"
              class="message-item"
              :class="message.role"
            >
              <!-- 移除头像区域，直接显示消息内容 -->
              <div class="message-content">
                <div class="message-bubble">
                  <div
                    class="message-text"
                    :class="{
                      streaming:
                        message.role === 'assistant' &&
                        chatStore.streaming &&
                        index === chatStore.messages.length - 1,
                    }"
                    v-html="
                      renderMarkdown(
                        message.role === 'assistant' &&
                          chatStore.streaming &&
                          index === chatStore.messages.length - 1
                          ? message.content || chatStore.currentStreamingMessage
                          : message.content || ''
                      )
                    "
                  />
                </div>
                <div class="message-time">
                  {{ formatMessageTime(message.timestamp) }}
                </div>
              </div>
            </div>

            <!-- Typing indicator -->
            <div v-if="chatStore.streaming" class="typing-indicator">
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <!-- 上下文状态标签 -->
      <div class="context-status-tags">
        <el-tag
          :type="getTagType('docName')"
          size="small"
          class="status-tag clickable-tag"
          :title="getTagTitle('docName')"
          @click="toggleTagActive('docName')"
        >
          Doc Name
        </el-tag>

        <el-tag
          :type="getTagType('docText')"
          size="small"
          class="status-tag clickable-tag"
          :title="getTagTitle('docText')"
          @click="toggleTagActive('docText')"
        >
          All Doc Text
        </el-tag>

        <el-tag
          :type="getTagType('selected')"
          size="small"
          class="status-tag clickable-tag"
          :title="getTagTitle('selected')"
          @click="toggleTagActive('selected')"
        >
          Selected
        </el-tag>

        <el-button
          v-if="isNativePdfViewer"
          size="small"
          @click="handleAddPdfText"
          class="add-pdf-text-btn"
        >
          <el-icon><Scissor /></el-icon>
          添加PDF选中文本
        </el-button>
      </div>

      <div class="input-area vertical-input">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            :disabled="chatStore.loading || chatStore.streaming"
            @keydown="handleKeyDown"
            resize="none"
            class="input-message"
          />

          <!-- 模式选择按钮 - 放在输入框内部左侧 -->
          <div class="mode-buttons-overlay">
            <!-- 按钮1: 学习模式 -->
            <el-dropdown
              @command="handleModeCommand"
              trigger="click"
              class="mode-dropdown-compact"
            >
              <el-button class="mode-btn-compact">
                <span>{{ currentMode }}</span>
                <el-icon class="dropdown-icon"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="general">常规模式</el-dropdown-item>
                  <el-dropdown-item command="teaching">教学模式</el-dropdown-item>
                  <el-dropdown-item command="summary">知识总结</el-dropdown-item>
                  <el-dropdown-item command="programming">编程学习</el-dropdown-item>
                  <el-dropdown-item command="exam">考试模式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 按钮2: 思考深度 -->
            <el-dropdown
              @command="handleThinkingCommand"
              trigger="click"
              class="mode-dropdown-compact"
            >
              <el-button class="mode-btn-compact">
                <span>{{ currentThinking }}</span>
                <el-icon class="dropdown-icon"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="deep">深度思考</el-dropdown-item>
                  <el-dropdown-item command="normal">常规思考</el-dropdown-item>
                  <el-dropdown-item command="quick">快速回答</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 按钮3: 预设提示词 -->
            <el-dropdown
              @command="handlePromptCommand"
              trigger="click"
              class="mode-dropdown-compact"
            >
              <el-button class="mode-btn-compact">
                <span>{{ currentPrompt }}</span>
                <el-icon class="dropdown-icon"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="custom">自定义提示</el-dropdown-item>
                  <el-dropdown-item command="explain">详细解释</el-dropdown-item>
                  <el-dropdown-item command="example">举例说明</el-dropdown-item>
                  <el-dropdown-item command="step">分步骤</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 操作按钮 - 保持在右侧 -->
          <div class="input-actions-overlay">
            <el-button
              link
              size="small"
              @click="startNewChat"
              title="新建对话"
              class="action-btn"
            >
              <el-icon size="16"><plus /></el-icon>
            </el-button>
            <el-button
              link
              size="small"
              @click="showConversations = !showConversations"
              title="对话历史"
              class="action-btn"
            >
              <el-icon size="16"><chat-line-square /></el-icon>
            </el-button>
            <el-button
              type="primary"
              size="small"
              :loading="chatStore.loading || chatStore.streaming"
              :disabled="!inputMessage.trim()"
              @click="sendMessage"
              class="send-btn"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  ChatLineSquare,
  Delete,
  ChatDotRound,
  Scissor,
  ArrowDown,
} from "@element-plus/icons-vue";
import { marked } from "marked";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import { useChatStore } from "@/stores/chat";
import { useDocumentStore } from "@/stores/document";

// Props
interface Props {
  documentId?: number;
  selectedText?: string;
  pdfViewerType?: "native" | "new";
}

const props = withDefaults(defineProps<Props>(), {
  documentId: undefined,
  selectedText: "",
  pdfViewerType: "new",
});

// Emits
const emit = defineEmits<{
  textSelected: [text: string];
}>();

// Stores
const chatStore = useChatStore();
const documentStore = useDocumentStore();

// State
const inputMessage = ref("");
const showConversations = ref(false);
const scrollbarRef = ref();

// 模式选择状态
const currentMode = ref("常规模式");
const currentThinking = ref("深度思考");
const currentPrompt = ref("预设提示");

// 标签激活状态
const tagActiveStates = ref({
  docName: false,
  docText: false,
  selected: false
});

// 标签用户设置状态（记录用户的具体设置：null=未设置, true=用户激活, false=用户取消）
const tagUserSetStates = ref<{
  docName: boolean | null;
  docText: boolean | null;
  selected: boolean | null;
}>({
  docName: null,
  docText: null,
  selected: null
});

// Computed
// 获取当前文档
const currentDocument = computed(() => {
  if (props.documentId) {
    return documentStore.documents.find((d) => d.id === props.documentId) || null;
  }
  return null;
});

const documentName = computed(() => {
  return currentDocument.value?.original_filename || "";
});

const isPdfSelected = computed(() => {
  return currentDocument.value?.file_type === "pdf" || false;
});

const isNativePdfViewer = computed(() => {
  return isPdfSelected.value && props.pdfViewerType === "native";
});

// 文档内容相关计算属性
const documentContent = computed(() => {
  return currentDocument.value?.content || "";
});

const documentContentPreview = computed(() => {
  if (!documentContent.value) return "无文档内容";
  const preview = documentContent.value.substring(0, 1000);
  return preview.length < documentContent.value.length ? `${preview}...` : preview;
});

// 标签管理方法
const getTagType = (tagName: string) => {
  const hasContent = getTagHasContent(tagName);
  const isActive = tagActiveStates.value[tagName as keyof typeof tagActiveStates.value];

  if (!hasContent) return 'info';  // 灰色：无内容
  if (isActive) return 'success';  // 绿色：用户激活
  return 'warning';                // 黄色：有内容但未激活
};

const getTagHasContent = (tagName: string) => {
  switch (tagName) {
    case 'docName':
      return !!(props.documentId && currentDocument.value && documentName.value);
    case 'docText':
      return !!(props.documentId && currentDocument.value && documentContent.value);
    case 'selected':
      return !!(props.selectedText && props.selectedText.trim());
    default:
      return false;
  }
};

const getTagTitle = (tagName: string) => {
  switch (tagName) {
    case 'docName':
      return documentName.value || '无文档';
    case 'docText':
      return documentContentPreview.value;
    case 'selected':
      return props.selectedText || '无选择文本';
    default:
      return '';
  }
};

const toggleTagActive = (tagName: string) => {
  const hasContent = getTagHasContent(tagName);
  if (!hasContent) return; // 无内容时不允许激活

  const key = tagName as keyof typeof tagActiveStates.value;
  tagActiveStates.value[key] = !tagActiveStates.value[key];

  // 记录用户的具体设置（激活=true，取消=false）
  tagUserSetStates.value[key] = tagActiveStates.value[key];
};

// Methods
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;

  const message = inputMessage.value.trim();
  inputMessage.value = "";

  // 只传递激活的上下文信息
  const activeDocumentId = tagActiveStates.value.docName || tagActiveStates.value.docText
    ? props.documentId
    : undefined;
  const activeSelectedText = tagActiveStates.value.selected
    ? props.selectedText
    : undefined;

  try {
    await chatStore.sendMessage(
      message,
      activeDocumentId,
      activeSelectedText,
      true, // Use streaming
      {
        mode: currentMode.value,
        thinking: currentThinking.value,
        prompt: currentPrompt.value,
        activeContexts: {
          docName: tagActiveStates.value.docName,
          docText: tagActiveStates.value.docText,
          selected: tagActiveStates.value.selected
        }
      }
    );

    // Scroll to bottom after sending
    await nextTick();
    scrollToBottom();
  } catch (error) {
    ElMessage.error("发送消息失败");
    console.error("Send message error:", error);
  }
};

const startNewChat = () => {
  chatStore.startNewConversation();
  showConversations.value = false;
};

// 模式选择处理方法
const handleModeCommand = (command: string) => {
  const modeMap: Record<string, string> = {
    general: "常规模式",
    teaching: "教学模式",
    summary: "知识总结",
    programming: "编程学习",
    exam: "考试模式",
  };
  currentMode.value = modeMap[command] || "常规模式";

  // 根据模式设置默认激活状态
  updateTagActiveStatesForMode(currentMode.value);

  ElMessage.success(`已切换到${currentMode.value}`);
};

const updateTagActiveStatesForMode = (mode: string) => {
  const isGeneralMode = mode === "常规模式";

  // 只更新用户未主动设置过的标签（null表示未设置）
  // 常规模式：默认不激活任何标签
  // 其他模式：默认激活有内容的标签

  if (tagUserSetStates.value.docName === null) {
    tagActiveStates.value.docName = !isGeneralMode && getTagHasContent('docName');
  }

  if (tagUserSetStates.value.docText === null) {
    tagActiveStates.value.docText = !isGeneralMode && getTagHasContent('docText');
  }

  if (tagUserSetStates.value.selected === null) {
    tagActiveStates.value.selected = !isGeneralMode && getTagHasContent('selected');
  }
};

// 重置用户设置状态（可选功能，用于清除用户的手动设置）
const resetUserTagSettings = () => {
  tagUserSetStates.value.docName = null;
  tagUserSetStates.value.docText = null;
  tagUserSetStates.value.selected = null;

  // 重新应用模式默认设置
  updateTagActiveStatesForMode(currentMode.value);
};

const handleThinkingCommand = (command: string) => {
  const thinkingMap: Record<string, string> = {
    deep: "深度思考",
    normal: "常规思考",
    quick: "快速回答",
  };
  currentThinking.value = thinkingMap[command] || "深度思考";
  ElMessage.success(`已切换到${currentThinking.value}`);
};

const handlePromptCommand = (command: string) => {
  const promptMap: Record<string, string> = {
    custom: "自定义提示",
    explain: "详细解释",
    example: "举例说明",
    step: "分步骤",
  };
  currentPrompt.value = promptMap[command] || "预设提示";
  ElMessage.success(`已选择${currentPrompt.value}`);
};

const selectConversation = async (conversationId: number) => {
  try {
    await chatStore.selectConversation(conversationId);
    showConversations.value = false;
    await nextTick();
    scrollToBottom();
  } catch (error) {
    ElMessage.error("加载对话失败");
  }
};

const deleteConversation = async (conversationId: number) => {
  try {
    await chatStore.deleteConversation(conversationId);
    ElMessage.success("对话删除成功");
  } catch (error) {
    ElMessage.error("删除对话失败");
  }
};

const handleAddPdfText = async () => {
  let clipboardText = "";
  try {
    // 尝试从剪贴板读取文本
    // 这会触发浏览器的权限请求（如果用户尚未授权）
    clipboardText = await navigator.clipboard.readText();
  } catch (err) {
    console.warn("无法自动读取剪贴板，需要用户手动粘贴:", err);
  }

  ElMessageBox.prompt("请在此处粘贴或确认您从PDF中复制的文本：", "添加PDF选中文本", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    inputType: "textarea",
    // 将读取到的剪贴板内容作为初始值
    inputValue: clipboardText,
    customClass: "pdf-text-prompt",
  })
    .then(({ value }) => {
      if (value && value.trim()) {
        const trimmedText = value.trim();
        documentStore.setSelectedText(trimmedText);
        emit("textSelected", trimmedText); // 通知父组件更新选中文本
        ElMessage.success("PDF选中文本已添加");
      } else {
        ElMessage.warning("您没有输入任何文本");
      }
    })
    .catch(() => {
      // 用户取消
    });
};

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const scrollToBottom = () => {
  if (scrollbarRef.value) {
    const scrollbar = scrollbarRef.value;
    scrollbar.setScrollTop(scrollbar.wrapRef.scrollHeight);
  }
};

const renderMarkdown = (text: string) => {
  if (!text) return "";

  try {
    const renderer = new marked.Renderer();
    renderer.code = function (code: string, infostring: string | undefined) {
      const lang = (infostring || "").split(/\s+/)[0];
      const label = lang ? lang : "code";
      let highlighted = "";
      if (lang && hljs.getLanguage(lang)) {
        highlighted = hljs.highlight(code, { language: lang }).value;
      } else {
        highlighted = hljs.highlightAuto(code).value;
      }
      return `
        <div class="custom-code-block">
          <div class="code-block-header">
            <span class="code-lang-label" translate="no">${label}</span>
            <button class="copy-code-btn" onclick="window.copyCodeBlock && window.copyCodeBlock(this)" translate="no">复制</button>
          </div>
          <pre translate="no"><code class="hljs ${lang}">${highlighted}</code></pre>
        </div>
      `;
    };
    marked.setOptions({
      breaks: true,
      gfm: true,
      renderer,
    });
    return marked(text);
  } catch (error) {
    console.error("Markdown render error:", error);
    return text;
  }
};

// 声明 window.copyCodeBlock 类型
interface WindowWithCopy extends Window {
  copyCodeBlock?: (btn: HTMLButtonElement) => void;
}

declare const window: WindowWithCopy;

if (typeof window !== "undefined") {
  window.copyCodeBlock = function (btn: HTMLButtonElement) {
    const code =
      btn.parentElement?.nextElementSibling?.querySelector("code")?.textContent || "";
    navigator.clipboard.writeText(code).then(() => {
      btn.innerText = "已复制";
      setTimeout(() => (btn.innerText = "复制"), 1200);
    });
  };
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatMessageTime = (timestamp?: string) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// Watch for new messages to auto-scroll
watch(
  () => chatStore.messages.length,
  () => {
    nextTick(() => {
      scrollToBottom();
    });
  }
);

// Watch for streaming updates to auto-scroll
watch(
  () => chatStore.currentStreamingMessage,
  () => {
    nextTick(() => {
      scrollToBottom();
    });
  }
);

// 监听模式变化，更新标签状态
watch(currentMode, () => {
  updateTagActiveStatesForMode(currentMode.value);
}, { immediate: true });

// 监听内容变化，但不重置用户设置
watch([() => props.documentId, () => props.selectedText], () => {
  // 当内容变化时，只对用户未设置过的标签应用默认逻辑
  updateTagActiveStatesForMode(currentMode.value);

  // 处理内容变化的特殊情况
  handleContentChange();
});

const handleContentChange = () => {
  // 对每个标签进行处理
  ['docName', 'docText', 'selected'].forEach(tagName => {
    const key = tagName as keyof typeof tagActiveStates.value;
    const hasContent = getTagHasContent(tagName);
    const userSetting = tagUserSetStates.value[key];

    if (!hasContent) {
      // 内容为空时，强制取消激活（但保留用户设置记录）
      tagActiveStates.value[key] = false;
    } else if (userSetting !== null) {
      // 有内容且用户设置过，恢复用户的设置
      tagActiveStates.value[key] = userSetting;
    }
    // 如果有内容但用户未设置过，由 updateTagActiveStatesForMode 处理
  });
};

// Lifecycle
onMounted(async () => {
  try {
    await chatStore.fetchConversations();
  } catch (error) {
    // Error is already handled in store
  }
});
</script>

<style lang="scss" scoped>
.chat-interface.vertical-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $panel-middle-bg;
}

.main-content-vertical {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}

.messages-container {
  flex: 1 1 auto;
  min-height: 0;
  height: 0;
  display: flex;
  flex-direction: column;
}

.input-area.vertical-input {
  border-top: 1px solid $border-color-light;
  background: $bg-color;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
}

.input-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

.input-message {
  width: 100%;
  min-height: 80px;
  max-height: 120px;

  :deep(.el-textarea__inner) {
    padding-left: 16px !important; // 恢复正常左侧padding
    padding-right: 140px !important; // 为右侧按钮留出空间
    padding-bottom: 50px !important; // 为底部按钮留出空间
    padding-top: 16px !important; // 正常顶部padding
    min-height: 80px !important;
    resize: none !important;
    box-sizing: border-box;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    line-height: 1.5;

    &:focus {
      border-color: #409eff;
      outline: none;
    }
  }
}

.input-actions-overlay {
  position: absolute;
  right: 12px;
  bottom: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 4px 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  pointer-events: auto;

  // 确保按钮容器不会被遮挡
  &::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: transparent;
    z-index: -1;
  }
}

.input-actions-overlay .el-button {
  margin: 0;
  min-width: 36px;
  height: 36px;
  padding: 0 10px;
  box-sizing: border-box;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.input-actions-overlay .el-button--link {
  background: transparent;
  border: none;
  color: #606266;

  &:hover {
    background: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }

  &:active {
    background: rgba(64, 158, 255, 0.2);
  }
}

.input-actions-overlay .el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
  font-weight: 500;

  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
  }

  &:active {
    background: #3a8ee6;
    border-color: #3a8ee6;
  }

  &:disabled {
    background: #a0cfff;
    border-color: #a0cfff;
    cursor: not-allowed;
  }
}

// 特定按钮样式
.input-actions-overlay .action-btn {
  min-width: 36px;
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    margin: 0;
  }
}

.input-actions-overlay .send-btn {
  min-width: 60px;
  height: 36px;
  padding: 0 12px;
  font-size: 14px;
  font-weight: 500;
}

.conversation-list {
  .conversation-item {
    display: flex;
    align-items: center;
    padding: $spacing-md;
    border-bottom: 1px solid $border-color-lighter;
    cursor: pointer;
    transition: $transition-base;

    &:hover {
      background: $bg-color-light;
    }

    &.active {
      background: $primary-color;
      color: white;
    }

    .conversation-info {
      flex: 1;

      .conversation-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: $spacing-xs;
      }

      .conversation-time {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }
}

.messages-scroll {
  height: 100%;
}

.messages-list {
  padding: $spacing-lg;
}

.message-item {
  display: flex;
  margin-bottom: $spacing-lg;

  &.user {
    flex-direction: row-reverse;

    .message-content {
      align-items: flex-end;

      .message-bubble {
        background: $chat-user-bg;
        color: $chat-user-text;
      }
    }
  }

  &.assistant {
    .message-bubble {
      background: $chat-assistant-bg;
      color: $chat-assistant-text;
    }
  }

  .message-content {
    display: flex;
    flex-direction: column;
    /* 移除 max-width 限制，让消息内容占满可用宽度 */
    width: 100%;

    .message-bubble {
      padding: $spacing-md $spacing-lg;
      border-radius: $border-radius-lg;
      word-break: break-word;

      .message-text {
        line-height: 1.5;

        &.streaming::after {
          content: "|";
          animation: blink 1s infinite;
        }

        :deep(pre) {
          background: lab(98.27% 0.01 -0.01) !important;
          padding: 12px;
          border-radius: 6px;
          overflow-x: auto;
        }

        :deep(code) {
          background: transparent !important;
          padding: 0 4px;
          border-radius: 0;
          font-family: "Monaco", "Consolas", monospace;
        }
      }
    }

    .message-time {
      font-size: 12px;
      color: $text-color-placeholder;
      margin-top: $spacing-xs;
      padding: 0 $spacing-md;
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-lg;

  .typing-dots {
    display: flex;
    gap: 4px;
    margin-left: 48px;

    span {
      width: 8px;
      height: 8px;
      background: $text-color-placeholder;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

.context-info {
  padding: $spacing-sm $spacing-lg;
  background: $bg-color-lighter;
  border-bottom: 1px solid $border-color-lighter;

  .context-items {
    display: flex;
    gap: $spacing-sm;
    flex-wrap: wrap;
  }
}

.empty-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: $text-color-secondary;
}

// 代码块样式已移至全局样式文件 (main.scss)

// 上下文状态标签样式
.context-status-tags {
  display: flex;
  gap: 8px;
  padding: 8px $spacing-lg;
  background: #fafbfc;
  border-bottom: 1px solid #e1e4e8;
  flex-wrap: wrap;
  align-items: center;

  .status-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    max-width: 200px;
    border-radius: 4px;
    font-size: 12px;
    padding: 4px 8px;

    &.clickable-tag {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .tag-label {
      font-weight: 600;
      white-space: nowrap;
      opacity: 0.8;
    }

    .tag-content {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      min-width: 0;
    }

    // 成功状态（激活状态 - 绿色）
    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #22c55e;
      color: #16a34a;

      .tag-label {
        color: #15803d;
      }
    }

    // 警告状态（有内容但未激活 - 黄色）
    &.el-tag--warning {
      background-color: #fffbeb;
      border-color: #f59e0b;
      color: #d97706;

      .tag-label {
        color: #92400e;
      }
    }

    // 信息状态（无内容 - 灰色）
    &.el-tag--info {
      background-color: #f8f9fa;
      border-color: #d1d5db;
      color: #6b7280;

      .tag-label {
        color: #4b5563;
      }
    }
  }

  .add-pdf-text-btn {
    margin-left: auto;
    font-size: 12px;
    padding: 4px 8px;
    height: 24px;
    border-radius: 4px;
    background: #f0f9ff;
    border-color: #3b82f6;
    color: #1d4ed8;

    &:hover {
      background: #dbeafe;
      border-color: #2563eb;
      color: #1e40af;
    }

    .el-icon {
      font-size: 12px;
    }
  }
}

// 模式选择按钮样式 - 紧凑型内嵌样式
.mode-buttons-overlay {
  position: absolute;
  bottom: 8px;
  left: 12px;
  display: flex;
  gap: 4px;
  align-items: center;
  z-index: 10;
  pointer-events: auto;
}

.mode-dropdown-compact {
  .mode-btn-compact {
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 6px 10px;
    font-size: 14px;
    color: #606266;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 70px;
    max-width: 90px;
    height: 36px;
    justify-content: space-between;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      border-color: #409eff;
      color: #409eff;
      background: #f0f9ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &:focus {
      border-color: #409eff;
      outline: none;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    .dropdown-icon {
      font-size: 12px;
      transition: transform 0.2s ease;
      opacity: 0.8;
      flex-shrink: 0;
    }

    &.is-active .dropdown-icon {
      transform: rotate(180deg);
    }

    span {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      text-align: left;
    }
  }
}

// 下拉菜单样式优化
:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  padding: 4px 0;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    color: #606266;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f7fa;
      color: #409eff;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

// Animations
@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
