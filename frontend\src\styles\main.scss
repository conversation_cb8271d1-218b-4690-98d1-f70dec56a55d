@use './variables.scss' as *;

// Global styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: $text-color-primary;
  background-color: $bg-color-light;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

// Scrollbar styles
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $bg-color-lighter;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: $border-color;
  border-radius: 3px;
  
  &:hover {
    background: $border-color-light;
  }
}

// Utility classes
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.cursor-pointer {
  cursor: pointer;
}

.user-select-none {
  user-select: none;
}

// Spacing utilities
.m-0 { margin: 0; }
.m-1 { margin: $spacing-xs; }
.m-2 { margin: $spacing-sm; }
.m-3 { margin: $spacing-md; }
.m-4 { margin: $spacing-lg; }
.m-5 { margin: $spacing-xl; }

.p-0 { padding: 0; }
.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; }

// Text utilities
.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-info { color: $info-color; }

.text-regular { color: $text-color-regular; }
.text-secondary { color: $text-color-secondary; }
.text-placeholder { color: $text-color-placeholder; }

// Background utilities
.bg-white { background-color: $bg-color; }
.bg-light { background-color: $bg-color-light; }
.bg-lighter { background-color: $bg-color-lighter; }

// Border utilities
.border { border: 1px solid $border-color; }
.border-light { border: 1px solid $border-color-light; }
.border-lighter { border: 1px solid $border-color-lighter; }

.border-radius { border-radius: $border-radius-md; }
.border-radius-lg { border-radius: $border-radius-lg; }

// Shadow utilities
.shadow-light { box-shadow: $box-shadow-light; }
.shadow-base { box-shadow: $box-shadow-base; }
.shadow-dark { box-shadow: $box-shadow-dark; }

// Animation utilities
.transition { transition: $transition-base; }
.transition-fade { transition: $transition-fade; }

// Loading animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spin {
  animation: spin 1s linear infinite;
}

// Fade in animation
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// Slide in animation
@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

// Card break animation
@keyframes cardBreak {
  0% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.1) rotate(5deg); opacity: 0.8; }
  100% { transform: scale(0) rotate(15deg); opacity: 0; }
}

.card-break {
  animation: cardBreak 0.6s ease-in-out forwards;
}

// Pulse animation for loading
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

// 代码块样式 - 全局样式确保动态生成的内容能正确应用
.custom-code-block {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  background: #f8f9fa;
  border: 1px solid #e9ecef;

  // 防止浏览器自动翻译
  * {
    translate: no;
  }

  .code-block-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #d0d2d3;
    padding: 8px 12px;
    border-bottom: none;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    position: relative;
  }

  .code-lang-label {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
    letter-spacing: 0.3px;
    display: flex;
    align-items: center;
    font-family: "SF Mono", "Monaco", "Consolas", monospace;
    text-transform: lowercase;

    &::before {
      content: "●";
      color: #28a745;
      margin-right: 5px;
      font-size: 10px;
    }
  }

  .copy-code-btn {
    background: rgba(74, 85, 104, 0.08);
    border: 1px solid rgba(74, 85, 104, 0.15);
    color: #718096;
    font-size: 11px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    font-weight: 500;

    &::before {
      content: "⧉";
      margin-right: 3px;
      font-size: 10px;
      opacity: 0.8;
    }

    &:hover {
      background: #409eff;
      border-color: #409eff;;
      color: #f4f4f4;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
      background: rgba(74, 85, 104, 0.15);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  pre {
    margin: 0;
    background: #fafbfc;
    padding: 12px 16px;
    border-radius: 0;
    font-size: 13px;
    line-height: 1.6;
    overflow-x: auto;
    color: #24292e;
    border-top: 1px solid #e1e4e8;

    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c8cd;
      border-radius: 4px;

      &:hover {
        background: #a8b2ba;
      }
    }
  }

  code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", "Courier New", monospace;
    font-size: 13px;
    font-weight: 400;
  }
}
