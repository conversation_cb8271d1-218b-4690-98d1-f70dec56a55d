"""
AI服务提示词模板
包含各种类型的提示词模板，便于维护和管理
"""

from typing import Dict, Any


class PromptTemplates:
    """提示词模板类"""
    
    # 系统消息模板
    SYSTEM_MESSAGES = {
        "knowledge_assistant": "你是一个知识学习助手，帮助用户理解和学习知识内容。",
        "question_generator": "你是一个专业的题目生成助手，擅长根据学习内容生成高质量的测试题目。"
    }
    
    # 题目生成模板
    QUESTION_GENERATION = {
        "choice_questions": """基于以下内容生成{count}道选择题，难度为{difficulty}。

内容：
{content}

请按照以下JSON格式返回：
[
  {{
    "question": "题目内容",
    "options": ["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"],
    "answer": "A",
    "explanation": "答案解析"
  }}
]

要求：
1. 题目要基于给定内容
2. 选项要有一定的迷惑性
3. 答案解析要清晰明了
4. 只返回JSON格式，不要其他文字""",
        
        "judgment_questions": """基于以下内容生成{count}道判断题，难度为{difficulty}。

内容：
{content}

请按照以下JSON格式返回：
[
  {{
    "question": "题目内容（判断对错）",
    "answer": "true",
    "explanation": "答案解析"
  }}
]

要求：
1. 题目要基于给定内容
2. 答案只能是true或false
3. 答案解析要清晰明了
4. 只返回JSON格式，不要其他文字"""
    }
    
    # 上下文消息模板
    CONTEXT_TEMPLATES = {
        "document_reference": "\n\n参考资料：",
        "document_content": "\n文档内容：{content}...",
        "selected_text": "\n用户选择的文本：{text}"
    }
    
    @classmethod
    def get_system_message(cls, message_type: str) -> str:
        """获取系统消息"""
        return cls.SYSTEM_MESSAGES.get(message_type, cls.SYSTEM_MESSAGES["knowledge_assistant"])
    
    @classmethod
    def get_question_prompt(cls, question_type: str, **kwargs) -> str:
        """获取题目生成提示词"""
        if question_type == "choice":
            template = cls.QUESTION_GENERATION["choice_questions"]
        elif question_type == "judgment":
            template = cls.QUESTION_GENERATION["judgment_questions"]
        else:
            raise ValueError(f"Unsupported question type: {question_type}")
        
        return template.format(**kwargs)
    
    @classmethod
    def build_context_content(
        cls, 
        base_content: str,
        document_content: str = "",
        selected_text: str = ""
    ) -> str:
        """构建带上下文的系统消息内容"""
        content = base_content
        
        if document_content or selected_text:
            content += cls.CONTEXT_TEMPLATES["document_reference"]
            
            if document_content:
                content += cls.CONTEXT_TEMPLATES["document_content"].format(
                    content=document_content[:1000]
                )
            
            if selected_text:
                content += cls.CONTEXT_TEMPLATES["selected_text"].format(
                    text=selected_text
                )
        
        return content




# 全局模板实例
prompt_templates = PromptTemplates()
