#!/bin/bash

echo "🚀 Starting Knowledge Assistant..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi

# Start backend
echo "📡 Starting backend server..."
cd backend
if [ ! -d "venv" ]; then
    echo "🔧 Creating virtual environment..."
    python3 -m venv venv
fi

source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null

echo "📦 Installing backend dependencies..."
pip install -r requirements.txt

echo "🗄️ Initializing database..."
python -c "
from app.core.database import engine, Base
from app.models import document, conversation, question
Base.metadata.create_all(bind=engine)
print('Database initialized successfully!')
"

echo "🌐 Starting FastAPI server on http://localhost:8000"
python main.py &
BACKEND_PID=$!

cd ..

# Start frontend
echo "🎨 Starting frontend server..."
cd frontend

echo "📦 Installing frontend dependencies..."
npm install

echo "🌐 Starting Vue.js server on http://localhost:5173"
npm run dev &
FRONTEND_PID=$!

cd ..

echo ""
echo "🎉 Knowledge Assistant is now running!"
echo ""
echo "📖 Access the application:"
echo "   Frontend: http://localhost:5173"
echo "   Backend API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "🛑 To stop the servers, press Ctrl+C"
echo ""

# Wait for interrupt
trap "echo '🛑 Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0" INT

wait
