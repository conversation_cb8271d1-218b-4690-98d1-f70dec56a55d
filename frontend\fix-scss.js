#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/App.vue',
  'src/views/HomeView.vue',
  'src/components/DocumentBrowser.vue',
  'src/components/ChatInterface.vue'
];

// 修复函数
function fixScssImports(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  
  // 移除自动注入的变量导入（因为vite.config.ts已经处理了）
  // 这些导入会导致重复导入警告
  const hasStyleSection = content.includes('<style lang="scss"');
  
  if (hasStyleSection) {
    // 如果有style section但没有scoped，添加scoped
    content = content.replace(/<style lang="scss">/, '<style lang="scss" scoped>');
    
    console.log(`已修复: ${filePath}`);
    fs.writeFileSync(fullPath, content, 'utf8');
  }
}

// 执行修复
console.log('开始修复SCSS导入问题...');

filesToFix.forEach(fixScssImports);

console.log('SCSS修复完成！');
console.log('\n请运行以下命令重新安装依赖：');
console.log('npm install');
console.log('\n然后重新启动开发服务器：');
console.log('npm run dev');
