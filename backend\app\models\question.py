from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, <PERSON>olean, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class Question(Base):
    __tablename__ = "questions"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=True)
    question_type = Column(String(20), nullable=False)  # choice, judgment, process_error
    difficulty = Column(String(10), nullable=False, default="medium")  # easy, medium, hard
    question_text = Column(Text, nullable=False)
    options = Column(JSON, nullable=True)  # For choice questions: ["A", "B", "C", "D"]
    choice_mapping = Column(JSON, nullable=True)  # For choice questions: {"A": "option1", "B": "option2"}
    incorrect_mermaid = Column(Text, nullable=True)  # For process_error questions: incorrect mermaid diagram
    correct_mermaid = Column(Text, nullable=True)  # For process_error questions: correct mermaid diagram
    correct_answer = Column(String(10), nullable=False)
    explanation = Column(Text, nullable=True)
    source_content = Column(Text, nullable=True)  # Related content from document
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship with document
    # document = relationship("Document")  # Commented out to avoid circular import
    
    # Relationship with answer records
    answer_records = relationship("AnswerRecord", back_populates="question", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Question(id={self.id}, type='{self.question_type}', difficulty='{self.difficulty}')>"

class AnswerRecord(Base):
    __tablename__ = "answer_records"
    
    id = Column(Integer, primary_key=True, index=True)
    question_id = Column(Integer, ForeignKey("questions.id"), nullable=False)
    user_answer = Column(String(10), nullable=False)
    is_correct = Column(Boolean, nullable=False)
    time_spent = Column(Integer, nullable=True)  # Time in seconds
    answered_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship with question
    question = relationship("Question", back_populates="answer_records")
    
    def __repr__(self):
        return f"<AnswerRecord(id={self.id}, question_id={self.question_id}, correct={self.is_correct})>"
