import json
import asyncio
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import httpx
from app.core.config import settings


# 选择题模型
class ChoiceQuestion(BaseModel):
    question: str = Field(description="问题描述")
    choice: Dict[str, str] = Field(description="问题选项，如 {'A': '12.5','B': '0','C': '-1','D': '25'}")
    answer: str = Field(description="问题答案,问题选项中的一项，如 A")
    level: str = Field(description="问题难易程度,简单，中等，困难其中一项")
    explanation: str = Field(description="问题解析,对正确答案进行解析")


class ChoiceQuestions(BaseModel):
    questions: List[ChoiceQuestion] = Field(description="问题列表")


# 判断题模型
class TrueFalseQuestion(BaseModel):
    question: str = Field(description="问题描述")
    answer: str = Field(description="问题答案,正确,错误")
    level: str = Field(description="问题难易程度,简单，中等，困难其中一项")
    explanation: str = Field(description="问题解析,对正确答案进行解析")


class TrueFalseQuestions(BaseModel):
    questions: List[TrueFalseQuestion] = Field(description="问题列表")


# 流程纠错题模型
class ProcessErrorCorrectionQuestion(BaseModel):
    question: str = Field(description="问题描述")
    answer: str = Field(description="问题答案,正确,错误")
    incorrect_mermaid: str = Field(description="错误的mermaid流程图")
    correct_mermaid: str = Field(description="正确的mermaid流程图")
    level: str = Field(description="问题难易程度,简单,中等,困难其中一项")
    explanation: str = Field(description="问题解析,对错误的流程图错误点进行纠错,给出正确解析")


class ProcessErrorCorrectionQuestions(BaseModel):
    questions: List[ProcessErrorCorrectionQuestion] = Field(description="问题列表")


class QuestionService:
    """问题生成服务，基于 knowledge/template_question.py 的设计"""
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 问题模板
        self.question_template = """\
以下关于{topic}的其中的一个知识点，请开启考试模式，针对知识点，提出10个问题，问题类型为{question_type},难易程度为简单：中等：困难6:3:1。问题和答案以json格式输出。
问题中涉及的公式需要以markdown的公式格式输出
    知识点：{knowledge}
 {format_instructions}
"""
        
        # 流程纠错问题模板
        self.process_error_question_template = """\
以下关于{topic}的其中的一个知识点，请开启考试模式，针对知识点，提出3个问题，问题类型为{question_type},难易程度为简单：中等：困难1:1:1。问题和答案以json格式输出。
问题中涉及的公式需要以markdown的公式格式输出,流程图以mermaid语法描述。
示例：以下为某某问题的的错误流程图，请找出并修正至少2处错误： 
```mermaid
graph TD
    A[输入X] --> B[线性投影Q]
    A --> C[线性投影K] 
    A --> D[线性投影V]
    B --> E[计算QKᵀ]
    C --> E
    E --> F[softmax]
    F --> G[乘积运算]
    D --> H[输出拼接]
    G --> H
    H --> I[线性投影]
```
**错误点提示**：
1. 某某环节问题
2. 某某关键步骤缺失
    知识点：{knowledge}
 {format_instructions}
"""

    def _get_format_instructions(self, question_type: str) -> str:
        """获取格式化指令"""
        import json
        if question_type == "choice":
            return json.dumps(ChoiceQuestions.model_json_schema(), indent=2, ensure_ascii=False)
        elif question_type == "judgment":
            return json.dumps(TrueFalseQuestions.model_json_schema(), indent=2, ensure_ascii=False)
        elif question_type == "process_error":
            return json.dumps(ProcessErrorCorrectionQuestions.model_json_schema(), indent=2, ensure_ascii=False)
        else:
            return json.dumps(ChoiceQuestions.model_json_schema(), indent=2, ensure_ascii=False)

    def _extract_topic_from_content(self, content: str) -> str:
        """从内容中提取主题"""
        # 简单的主题提取逻辑，可以根据需要改进
        lines = content.split('\n')
        for line in lines:
            if line.strip() and len(line.strip()) < 100:
                return line.strip()
        return "相关知识"

    async def _call_ai_api(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """调用AI API"""
        print(f"🔄 Calling AI API with {len(messages)} messages")
        payload = {
            "model": "deepseek-chat",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000,
            "stream": False
        }

        try:
            async with httpx.AsyncClient(timeout=120.0) as client:  # 增加到2分钟
                print(f"📡 Sending request to {self.base_url}/chat/completions")
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                print(f"📥 Received response with status: {response.status_code}")
                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"API Error: {response.status_code} - {response.text}"
                    print(f"❌ {error_msg}")
                    return {"error": error_msg}
        except Exception as e:
            error_msg = f"Exception calling AI API: {str(e)}"
            print(f"❌ {error_msg}")
            return {"error": error_msg}

    async def generate_questions(
        self,
        content: str,
        question_type: str = "choice",
        count: int = 10,
        topic: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成问题
        
        Args:
            content: 知识内容
            question_type: 问题类型 (choice, judgment, process_error)
            count: 问题数量
            topic: 主题（可选，如果不提供会自动提取）
        
        Returns:
            生成的问题字典，格式与 knowledge/question.json 一致
        """
        
        if not topic:
            topic = self._extract_topic_from_content(content)
        
        # 获取格式化指令
        format_instructions = self._get_format_instructions(question_type)
        
        # 选择模板
        if question_type == "process_error":
            template = self.process_error_question_template
        else:
            template = self.question_template
        
        # 智能截取内容 - 优先保留前面的重要内容
        max_content_length = 1500  # 减少内容长度以提高处理速度
        if len(content) > max_content_length:
            # 尝试在句号处截断，保持内容完整性
            truncated_content = content[:max_content_length]
            last_period = truncated_content.rfind('。')
            if last_period > max_content_length * 0.7:  # 如果句号位置合理
                content_to_use = truncated_content[:last_period + 1]
            else:
                content_to_use = truncated_content
        else:
            content_to_use = content

        # Content truncated for optimal processing

        # 构建提示词
        prompt = template.format(
            topic=topic,
            question_type=question_type,
            knowledge=content_to_use,
            format_instructions=format_instructions
        )
        
        messages = [
            {
                "role": "system", 
                "content": "你是一个专业的教育专家，擅长根据知识内容生成高质量的考试题目。请严格按照要求的JSON格式输出。"
            },
            {"role": "user", "content": prompt}
        ]
        
        try:
            print(f"🎯 Generating {question_type} questions for topic: {topic}")
            response = await self._call_ai_api(messages)

            if "error" in response:
                print(f"❌ AI API returned error: {response['error']}")
                return {"error": response["error"]}

            content_text = response["choices"][0]["message"]["content"]

            # 尝试解析JSON
            try:
                # 首先尝试直接解析
                questions_data = json.loads(content_text)
                return questions_data
            except json.JSONDecodeError:
                # 如果失败，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', content_text, re.DOTALL)
                if json_match:
                    try:
                        questions_data = json.loads(json_match.group())
                        return questions_data
                    except json.JSONDecodeError:
                        pass

                # 如果还是失败，返回错误
                return {"error": "Failed to parse AI response as JSON"}

        except Exception as e:
            return {"error": f"Error generating questions: {str(e)}"}

    def convert_to_database_format(
        self, 
        questions_data: Dict[str, Any], 
        question_type: str
    ) -> List[Dict[str, Any]]:
        """
        将生成的问题数据转换为数据库格式
        
        Args:
            questions_data: 生成的问题数据
            question_type: 问题类型
        
        Returns:
            数据库格式的问题列表
        """
        if "error" in questions_data:
            return []
        
        db_questions = []
        questions_list = questions_data.get("questions", [])
        
        for q in questions_list:
            db_question = {
                "question_text": q.get("question", ""),
                "question_type": question_type,
                "difficulty": self._map_difficulty(q.get("level", "中等")),
                "correct_answer": q.get("answer", ""),
                "explanation": q.get("explanation", "")
            }
            
            # 根据题型添加特定字段
            if question_type == "choice":
                db_question["options"] = list(q.get("choice", {}).values())
                db_question["choice_mapping"] = q.get("choice", {})
            elif question_type == "process_error":
                db_question["incorrect_mermaid"] = q.get("incorrect_mermaid", "")
                db_question["correct_mermaid"] = q.get("correct_mermaid", "")
            
            db_questions.append(db_question)
        
        return db_questions

    def _map_difficulty(self, chinese_level: str) -> str:
        """映射中文难度到英文"""
        mapping = {
            "简单": "easy",
            "中等": "medium", 
            "困难": "hard"
        }
        return mapping.get(chinese_level, "medium")


# 全局问题服务实例
question_service = QuestionService()