# backend/app/core/logging_config.py
import logging
import sys
from logging.handlers import RotatingFileHandler
import os

# --- 配置 ---
LOG_DIR = "logs"  # 日志文件存放目录
LOG_FILE = "app.log" # 日志文件名
LOG_LEVEL = logging.ERROR # 日志级别
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - %(message)s" # 日志格式
MAX_BYTES = 10 * 1024 * 1024  # 每个日志文件最大10MB
BACKUP_COUNT = 5 # 最多保留5个备份文件

# --- 设置 ---
def setup_logging():
    """
    配置日志系统
    """
    # 确保日志目录存在
    log_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', LOG_DIR)
    if not os.path.exists(log_dir_path):
        os.makedirs(log_dir_path)

    log_file_path = os.path.join(log_dir_path, LOG_FILE)

    # 创建一个 logger
    logger = logging.getLogger()
    logger.setLevel(LOG_LEVEL)

    # 如果logger已经有handlers，就不再添加，防止重复打印
    if logger.hasHandlers():
        logger.handlers.clear()

    # 创建一个 formatter
    formatter = logging.Formatter(LOG_FORMAT)

    # 创建一个 handler，用于写入日志文件
    # RotatingFileHandler 会在文件达到一定大小后自动创建新文件
    file_handler = RotatingFileHandler(
        filename=log_file_path,
        maxBytes=MAX_BYTES,
        backupCount=BACKUP_COUNT,
        encoding="utf-8"
    )
    file_handler.setFormatter(formatter)

    # 创建一个 handler，用于将日志输出到控制台
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)

    # 为 logger 添加 handlers
    logger.addHandler(file_handler)
    logger.addHandler(stream_handler)

    logging.info("日志系统配置完成。")
