## 1. 核心概念
- **自注意力（Self-Attention）**
  输入序列内部元素间的交互机制，计算每个元素与其他元素的关联权重。
- **缩放点积注意力（Scaled Dot-Product Attention）**
  通过查询（Q）、键（K）、值（V）矩阵计算注意力权重，公式：
  $\text{Attention}(Q,K,V)=\text{softmax}(\frac{QK^T}{\sqrt{d_k}})V$

## 2. 关键组件
### 2.1 多头注意力（Multi-Head Attention）
- 并行多个注意力头，捕获不同子空间的特征
- 计算流程：
  1. 线性投影生成多组Q/K/V
  2. 分别计算缩放点积注意力
  3. 拼接结果并通过线性层融合

### 2.2 位置编码（Positional Encoding）
- 解决Transformer缺少位置信息的问题
- 正弦/余弦函数编码：
  $PE_{(pos,2i)} = \sin(pos/10000^{2i/d_{model}})$
  $PE_{(pos,2i+1)} = \cos(pos/10000^{2i/d_{model}})$

## 3. 数学原理
- **Q/K/V矩阵**：查询（Query）、键（Key）、值（Value）
- **Softmax归一化**：将注意力分数转换为概率分布
- **缩放因子$\sqrt{d_k}$**：防止点积结果过大导致梯度消失

## 4. 优化技术
- **掩码注意力（Masked Attention）**
  解码时防止未来信息泄露（三角矩阵掩码）
- **残差连接 & Layer Normalization**
  缓解梯度消失，加速训练

## 5. 变体与扩展
- **稀疏注意力**：降低计算复杂度（如Longformer）
- **相对位置编码**：替代绝对位置编码（如Transformer-XL）
- **跨模态注意力**：应用于多模态任务（如ViT）

## 6. 典型应用
- 机器翻译（原始Transformer）
- BERT（双向注意力）
- GPT系列（自回归注意力）
- 视觉Transformer（图像分块处理）

## 7. 复杂度分析
- 时间复杂度：$O(n^2 \cdot d)$（n为序列长度）
- 空间复杂度：$O(n^2)$（需存储注意力矩阵）
