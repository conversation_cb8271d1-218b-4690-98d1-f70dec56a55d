// API Response Types
export interface Document {
  id: number
  filename: string
  original_filename: string
  file_type: string
  content: string
  file_size: number
  is_processed: boolean
  created_at: string
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: string
}

export interface Conversation {
  id: number
  title: string
  created_at: string
  updated_at: string
}

export interface Question {
  id: number
  question_type: 'choice' | 'judgment' | 'process_error'
  difficulty: 'easy' | 'medium' | 'hard'
  question_text: string
  options?: string[]
  choice_mapping?: Record<string, string>
  incorrect_mermaid?: string
  correct_mermaid?: string
  created_at: string
}

export interface QuestionWithAnswer extends Question {
  correct_answer: string
  explanation: string
}

export interface AnswerResponse {
  is_correct: boolean
  correct_answer: string
  explanation: string
  question_id: number
}

// Request Types
export interface ChatRequest {
  message: string
  conversation_id?: number
  document_id?: number
  selected_text?: string
  stream?: boolean
}

export interface GenerateQuestionsRequest {
  document_id?: number
  content?: string
  question_type: 'choice' | 'judgment' | 'process_error'
  difficulty: 'easy' | 'medium' | 'hard'
  count: number
}

// 新增：生成问题的响应类型，包含原始JSON数据
export interface GenerateQuestionsResponse {
  questions_data: any  // 原始生成的JSON数据，格式与knowledge目录一致
  saved_questions: Question[]  // 保存到数据库的问题列表
}

export interface AnswerRequest {
  question_id: number
  user_answer: string
  time_spent?: number
}

// UI State Types
export interface PanelWidths {
  left: number
  middle: number
  right: number
}

export interface PanelVisibility {
  left: boolean
  middle: boolean
  right: boolean
}

// File Upload Types
export interface UploadProgress {
  percentage: number
  status: 'uploading' | 'success' | 'error'
  message?: string
}

// Streaming Response Types
export interface StreamResponse {
  type: 'conversation_id' | 'content' | 'done' | 'error'
  data: any
}
