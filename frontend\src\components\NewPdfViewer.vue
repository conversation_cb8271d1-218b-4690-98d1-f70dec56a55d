<template>
  <div class="new-pdf-viewer" @mouseup="handleTextSelection">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <span class="page-info"> {{ currentPage }} / {{ totalPages }} </span>
      </div>

      <div class="toolbar-right">
        <el-button-group>
          <el-button size="small" @click="zoom(-0.25)" :disabled="scale <= 0.5">
            <el-icon><zoom-out /></el-icon>
          </el-button>
          <el-button size="small" @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button size="small" @click="zoom(0.25)" :disabled="scale >= 3">
            <el-icon><zoom-in /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="pdf-content-wrapper" ref="containerRef">
      <div class="pdf-loading" v-if="loading">
        <el-icon class="loading-spin"><loading /></el-icon>
        <p>正在加载PDF...</p>
      </div>

      <div class="pdf-error" v-else-if="error">
        <el-icon><warning /></el-icon>
        <p>PDF加载失败</p>
        <p class="error-detail">{{ error }}</p>
      </div>

      <div v-show="!loading && !error" class="pdf-pages-container">
        <div
          v-for="pageNumber in totalPages"
          :key="pageNumber"
          :ref="el => pageRefs[pageNumber - 1] = el as HTMLDivElement"
          class="pdf-page-item"
          :style="{
            width: `${pageViewports[pageNumber - 1]?.width}px`,
            height: `${pageViewports[pageNumber - 1]?.height}px`,
            marginBottom: '20px'
          }"
        >
          <canvas :ref="el => canvasRefs[pageNumber - 1] = el as HTMLCanvasElement" class="pdf-canvas"></canvas>
          <div :ref="el => textLayerRefs[pageNumber - 1] = el as HTMLDivElement" class="text-layer"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, markRaw, onUnmounted, nextTick } from "vue";
import {
  ZoomIn,
  ZoomOut,
  Loading,
  Warning,
} from "@element-plus/icons-vue";
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorker from "pdfjs-dist/build/pdf.worker?url";
import type {
  PDFDocumentProxy,
  PDFPageProxy,
} from "pdfjs-dist/types/src/display/api";
import type { PageViewport } from "pdfjs-dist/types/src/display/display_utils";

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

interface Props {
  pdfUrl?: string;
}

const props = defineProps<Props>();
const emit = defineEmits(["text-selected"]);

// Refs
const containerRef = ref<HTMLElement>();
const pageRefs = ref<HTMLDivElement[]>([]);
const canvasRefs = ref<HTMLCanvasElement[]>([]);
const textLayerRefs = ref<HTMLDivElement[]>([]);

// State
const loading = ref(true);
const error = ref("");
const currentPage = ref(1);
const totalPages = ref(0);
const scale = ref(1.0);
const pdfDocument = ref<PDFDocumentProxy | null>(null);
const pageViewports = ref<PageViewport[]>([]);
let observer: IntersectionObserver | null = null;

const loadPDF = async () => {
  if (!props.pdfUrl) return;

  loading.value = true;
  error.value = "";
  if (pdfDocument.value) {
    await pdfDocument.value.destroy();
    pdfDocument.value = null;
  }

  try {
    const loadingTask = pdfjsLib.getDocument(props.pdfUrl);
    pdfDocument.value = markRaw(await loadingTask.promise);
    totalPages.value = pdfDocument.value.numPages;
    
    await preparePages();
    await nextTick();
    await renderAllPages();
    setupIntersectionObserver();

  } catch (err: any) {
    error.value = err.message || "PDF加载失败";
    console.error("PDF loading error:", err);
  } finally {
    loading.value = false;
  }
};

const preparePages = async () => {
  if (!pdfDocument.value) return;
  const viewports: PageViewport[] = [];
  for (let i = 1; i <= totalPages.value; i++) {
    const page = await pdfDocument.value.getPage(i);
    viewports.push(markRaw(page.getViewport({ scale: scale.value })));
  }
  pageViewports.value = viewports;
};

const renderAllPages = async () => {
  if (!pdfDocument.value) return;
  const pagePromises = [];
  for (let i = 1; i <= totalPages.value; i++) {
    pagePromises.push(renderPage(i));
  }
  await Promise.all(pagePromises);
};

const renderPage = async (pageNum: number) => {
  if (!pdfDocument.value) return;

  try {
    const page: PDFPageProxy = await pdfDocument.value.getPage(pageNum);
    const viewport = page.getViewport({ scale: scale.value });
    
    const canvas = canvasRefs.value[pageNum - 1];
    const textLayer = textLayerRefs.value[pageNum - 1];
    if (!canvas || !textLayer) return;

    const context = canvas.getContext("2d");
    if (!context) throw new Error("Cannot get canvas context");

    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderTask = page.render({ canvasContext: context, viewport });
    
    const textContent = await page.getTextContent();
    textLayer.innerHTML = "";
    const textLayerTask = pdfjsLib.renderTextLayer({
      textContentSource: textContent,
      container: textLayer,
      viewport: viewport,
      textDivs: [],
    });

    await Promise.all([renderTask.promise, textLayerTask.promise]);

  } catch (err: any) {
    if (err.name !== "RenderingCancelledException") {
      console.error(`Page ${pageNum} rendering error:`, err);
    }
  }
};

const setupIntersectionObserver = () => {
  const options = {
    root: containerRef.value,
    rootMargin: "0px",
    threshold: 0.5, // Trigger when 50% of the page is visible
  };

  observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const pageIndex = pageRefs.value.findIndex(ref => ref === entry.target);
        if (pageIndex !== -1) {
          currentPage.value = pageIndex + 1;
        }
      }
    });
  }, options);

  pageRefs.value.forEach(ref => {
    if (ref) observer?.observe(ref);
  });
};

const zoom = (amount: number) => {
  const newScale = parseFloat((scale.value + amount).toFixed(2));
  if (newScale >= 0.5 && newScale <= 3) {
    scale.value = newScale;
  }
};

const resetZoom = () => {
  scale.value = 1.0;
};

const handleTextSelection = () => {
  const selection = window.getSelection();
  const selectedText = selection?.toString().trim();
  if (selectedText) {
    emit("text-selected", selectedText);
  } else {
    // 当没有选中文本时，发送空字符串来清除选择
    emit("text-selected", "");
  }
};

watch(
  () => props.pdfUrl,
  (newUrl) => {
    if (newUrl) loadPDF();
  },
  { immediate: true }
);

watch(scale, async (newScale, oldScale) => {
  if (pdfDocument.value && newScale !== oldScale) {
    loading.value = true;
    await preparePages();
    await nextTick();
    await renderAllPages();
    loading.value = false;
  }
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
  if (pdfDocument.value) {
    pdfDocument.value.destroy();
    pdfDocument.value = null;
  }
});
</script>

<style lang="scss" scoped>
.new-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
  user-select: none;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .page-info {
    font-size: 13px;
    color: #666;
    min-width: 60px;
    text-align: center;
  }
}

.pdf-content-wrapper {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  min-height: 0;
}

.pdf-loading,
.pdf-error {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;

  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .loading-spin {
    animation: spin 1s linear infinite;
  }

  .error-detail {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}

.pdf-pages-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pdf-page-item {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;

  .pdf-canvas {
    display: block;
  }

  .text-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    color: transparent;
    user-select: text;
    line-height: 1;

    :deep(span),
    :deep(br) {
      position: absolute;
      white-space: pre;
      cursor: text;
      transform-origin: 0% 0%;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
