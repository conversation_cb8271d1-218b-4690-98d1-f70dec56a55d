#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复所有前端问题...\n');

// 1. 修复 main.ts 中的导入问题
function fixMainTs() {
  const mainTsPath = path.join(__dirname, 'src/main.ts');
  
  if (fs.existsSync(mainTsPath)) {
    let content = fs.readFileSync(mainTsPath, 'utf8');
    
    // 确保正确的导入顺序和语法
    const newContent = `import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import './styles/main.scss'

const app = createApp(App)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')`;

    fs.writeFileSync(mainTsPath, newContent, 'utf8');
    console.log('✅ 修复了 main.ts');
  }
}

// 2. 创建 router/index.ts 如果不存在
function ensureRouter() {
  const routerDir = path.join(__dirname, 'src/router');
  const routerPath = path.join(routerDir, 'index.ts');
  
  if (!fs.existsSync(routerDir)) {
    fs.mkdirSync(routerDir, { recursive: true });
  }
  
  if (!fs.existsSync(routerPath)) {
    const routerContent = `import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    }
  ]
})

export default router`;

    fs.writeFileSync(routerPath, routerContent, 'utf8');
    console.log('✅ 创建了 router/index.ts');
  }
}

// 3. 修复 vite.config.ts
function fixViteConfig() {
  const vitePath = path.join(__dirname, 'vite.config.ts');
  
  if (fs.existsSync(vitePath)) {
    const viteContent = `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: \`@use "@/styles/variables.scss" as *;\`
      }
    }
  }
})`;

    fs.writeFileSync(vitePath, viteContent, 'utf8');
    console.log('✅ 修复了 vite.config.ts');
  }
}

// 4. 确保所有必要的目录存在
function ensureDirectories() {
  const dirs = [
    'src/components',
    'src/views', 
    'src/stores',
    'src/utils',
    'src/types',
    'src/styles'
  ];
  
  dirs.forEach(dir => {
    const fullPath = path.join(__dirname, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ 创建了目录: ${dir}`);
    }
  });
}

// 5. 修复 package.json 脚本
function fixPackageJson() {
  const packagePath = path.join(__dirname, 'package.json');
  
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 确保脚本正确
    packageJson.scripts = {
      ...packageJson.scripts,
      "dev": "vite",
      "build": "vue-tsc && vite build",
      "preview": "vite preview",
      "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"
    };
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2), 'utf8');
    console.log('✅ 修复了 package.json 脚本');
  }
}

// 执行所有修复
try {
  fixMainTs();
  ensureRouter();
  fixViteConfig();
  ensureDirectories();
  fixPackageJson();
  
  console.log('\n🎉 所有问题修复完成！');
  console.log('\n📋 接下来请执行：');
  console.log('1. npm install');
  console.log('2. npm run dev');
  
} catch (error) {
  console.error('❌ 修复过程中出现错误：', error.message);
}
