<template>
  <div class="pdf-viewer">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            size="small" 
            @click="previousPage" 
            :disabled="currentPage <= 1"
          >
            <el-icon><arrow-left /></el-icon>
          </el-button>
          <el-button 
            size="small" 
            @click="nextPage" 
            :disabled="currentPage >= totalPages"
          >
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </el-button-group>
        
        <span class="page-info">
          {{ currentPage }} / {{ totalPages }}
        </span>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button size="small" @click="zoomOut" :disabled="scale <= 0.5">
            <el-icon><zoom-out /></el-icon>
          </el-button>
          <el-button size="small" @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button size="small" @click="zoomIn" :disabled="scale >= 3">
            <el-icon><zoom-in /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="pdf-container" ref="containerRef">
      <div class="pdf-loading" v-if="loading">
        <el-icon class="loading-spin"><loading /></el-icon>
        <p>正在加载PDF...</p>
      </div>
      
      <div class="pdf-error" v-else-if="error">
        <el-icon><warning /></el-icon>
        <p>PDF加载失败</p>
        <p class="error-detail">{{ error }}</p>
      </div>
      
      <canvas 
        v-else
        ref="canvasRef" 
        class="pdf-canvas"
        :style="{ transform: `scale(${scale})` }"
      ></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  ZoomOut,
  Loading,
  Warning
} from '@element-plus/icons-vue'
import * as pdfjsLib from 'pdfjs-dist'
import 'pdfjs-dist/build/pdf.worker.entry'

// 设置PDF.js worker - 使用稳定的方式
if (typeof window !== 'undefined') {
  pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`
}

interface Props {
  pdfUrl?: string
  pdfData?: Uint8Array
}

const props = defineProps<Props>()

// Refs
const containerRef = ref<HTMLElement>()
const canvasRef = ref<HTMLCanvasElement>()

// State
const loading = ref(false)
const error = ref('')
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.0)
const pdfDocument = ref<any>(null)

// Methods
const loadPDF = async () => {
  if (!props.pdfUrl && !props.pdfData) return

  loading.value = true
  error.value = ''

  try {
    let loadingTask

    if (props.pdfData) {
      loadingTask = pdfjsLib.getDocument({ data: props.pdfData })
    } else if (props.pdfUrl) {
      // 使用简单的URL加载方式
      loadingTask = pdfjsLib.getDocument(props.pdfUrl)
    } else {
      throw new Error('No PDF source provided')
    }

    const pdf = await loadingTask.promise
    pdfDocument.value = pdf
    totalPages.value = pdf.numPages
    currentPage.value = 1

    await renderPage(1)
  } catch (err: any) {
    error.value = err.message || 'PDF加载失败'
    console.error('PDF loading error:', err)
  } finally {
    loading.value = false
  }
}

const renderPage = async (pageNum: number) => {
  if (!pdfDocument.value || !canvasRef.value) return

  try {
    // 使用更安全的方式获取页面
    const page = await pdfDocument.value.getPage(pageNum)
    const viewport = page.getViewport({ scale: 1.0 })

    const canvas = canvasRef.value
    const context = canvas.getContext('2d')

    if (!context) {
      throw new Error('Cannot get canvas context')
    }

    // 设置canvas尺寸
    canvas.height = viewport.height
    canvas.width = viewport.width

    // 渲染页面
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    }

    const renderTask = page.render(renderContext)
    await renderTask.promise

    currentPage.value = pageNum
  } catch (err: any) {
    error.value = err.message || '页面渲染失败'
    console.error('Page rendering error:', err)
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    renderPage(currentPage.value - 1)
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    renderPage(currentPage.value + 1)
  }
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.25)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25)
  }
}

const resetZoom = () => {
  scale.value = 1.0
}

// Watchers
watch(() => props.pdfUrl, () => {
  if (props.pdfUrl) {
    loadPDF()
  }
}, { immediate: true })

watch(() => props.pdfData, () => {
  if (props.pdfData) {
    loadPDF()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  loadPDF()
})
</script>

<style lang="scss" scoped>
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .page-info {
    font-size: 13px;
    color: #666;
    min-width: 60px;
    text-align: center;
  }
}

.pdf-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  min-height: 0;
}

.pdf-loading,
.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .loading-spin {
    animation: spin 1s linear infinite;
  }
  
  .error-detail {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}

.pdf-canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  transform-origin: top center;
  transition: transform 0.2s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
