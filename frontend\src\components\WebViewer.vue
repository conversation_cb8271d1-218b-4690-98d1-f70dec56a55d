<template>
  <div class="web-viewer">
    <div class="web-toolbar">
      <div class="toolbar-left">
        <el-button size="small" @click="refreshPage">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>

        <el-button size="small" @click="openInNewTab" type="primary">
          <el-icon><Link /></el-icon>
          新窗口打开
        </el-button>
      </div>

      <div class="toolbar-right">
        <span class="url-display">{{ displayUrl }}</span>
      </div>
    </div>

    <div class="embed-wrapper">
      <!-- 受限网站的特殊提示 -->
      <div v-if="showDirectWarning" class="restricted-warning">
        <el-icon><InfoFilled /></el-icon>
        <h3>网站访问提示</h3>
        <p>{{ displayUrl }} 不允许在iframe中嵌入显示</p>
        <p class="warning-detail">这是由于网站的安全策略（X-Frame-Options）限制。</p>
        <div class="warning-actions">
          <el-button @click="openInNewTab" type="primary" size="large">
            <el-icon><Link /></el-icon>
            在新窗口中打开
          </el-button>
          <el-button @click="tryEmbedAnyway" size="large">
            <el-icon><View /></el-icon>
            仍然尝试嵌入
          </el-button>
        </div>
      </div>

      <!-- 正常的iframe嵌入 -->
      <template v-else>
        <iframe
          ref="iframeRef"
          :src="iframeSrc"
          class="web-iframe"
          @load="onLoad"
          @error="handleError"
          :style="{ opacity: loaded ? 1 : 0 }"
          frameborder="0"
          allowfullscreen
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
        />

        <div v-if="!loaded && !error" class="loading-indicator">
          <el-icon class="loading-spin"><Loading /></el-icon>
          <p>正在加载网页...</p>
        </div>

        <div v-if="error" class="error-indicator">
          <el-icon><Warning /></el-icon>
          <p>网页加载失败</p>
          <p class="error-detail">{{ error }}</p>
          <div class="error-actions">
            <el-button @click="retryLoad" type="primary">重试</el-button>
            <el-button @click="openInNewTab">新窗口打开</el-button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Link,
  Loading,
  Warning,
  InfoFilled,
  View
} from '@element-plus/icons-vue'

interface Props {
  url: string
  content?: string
}

const props = defineProps<Props>()

// Refs
const iframeRef = ref<HTMLIFrameElement>()

// State
const loaded = ref(false)
const error = ref('')
const loadAttempts = ref(0)
const showDirectWarning = ref(false)

// Computed
const displayUrl = computed(() => {
  if (!props.url) return ''
  try {
    const url = new URL(props.url)
    return url.hostname
  } catch {
    return props.url
  }
})

const iframeSrc = computed(() => {
  if (!props.url) return ''

  // 检查是否需要使用代理
  const proxyUrl = getProxyUrl(props.url)
  if (proxyUrl) {
    return proxyUrl
  }

  // 对于某些网站，我们可能需要使用代理或者特殊处理
  if (props.url.includes('youtube.com') || props.url.includes('youtu.be')) {
    // YouTube 嵌入处理
    const videoId = extractYouTubeVideoId(props.url)
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}`
    }
  }

  // 确保URL格式正确
  let url = props.url
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://' + url
  }

  return url
})

// Methods
const extractYouTubeVideoId = (url: string): string | null => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return (match && match[2].length === 11) ? match[2] : null
}

const isRestrictedSite = (url: string): boolean => {
  const restrictedDomains = [
    'vuejs.org',
    'github.com',
    'stackoverflow.com',
    'medium.com',
    'twitter.com',
    'x.com',
    'facebook.com',
    'instagram.com',
    'linkedin.com',
    'reddit.com'
  ]

  return restrictedDomains.some(domain => url.includes(domain))
}

const getProxyUrl = (url: string): string | null => {
  if (!url) return null

  // 代理网站映射
  const proxyMappings = {
    'cn.vuejs.org': 'vue-docs',
    'vuejs.org': 'vue-docs',
    'react.dev': 'react-docs',
    'github.com': 'github',
    'stackoverflow.com': 'stackoverflow'
  }

  // 检查是否需要代理
  for (const [domain, siteKey] of Object.entries(proxyMappings)) {
    if (url.includes(domain)) {
      // 提取路径部分
      const urlObj = new URL(url)
      const path = urlObj.pathname + urlObj.search

      // 构建代理URL
      return `http://127.0.0.1:8000/api/proxy/${siteKey}${path}`
    }
  }

  return null
}

const onLoad = () => {
  loaded.value = true
  error.value = ''
  loadAttempts.value = 0
  console.log('网页加载成功')
}

const handleError = (event?: Event) => {
  loaded.value = false
  loadAttempts.value++

  console.warn('网页加载错误:', event)

  // 检查是否是X-Frame-Options或CSP限制
  if (props.url && (
    props.url.includes('vuejs.org') ||
    props.url.includes('github.com') ||
    props.url.includes('stackoverflow.com') ||
    props.url.includes('medium.com')
  )) {
    error.value = '此网站不允许在iframe中显示（安全策略限制），请点击"新窗口打开"查看完整内容'
  } else if (loadAttempts.value === 1) {
    error.value = '网页可能不支持嵌入显示，请尝试在新窗口中打开'
  } else {
    error.value = '网页加载失败，请检查网络连接或网址是否正确'
  }
}

const refreshPage = () => {
  if (iframeRef.value) {
    loaded.value = false
    error.value = ''
    iframeRef.value.src = iframeRef.value.src
  }
}

const openInNewTab = () => {
  window.open(props.url, '_blank')
}

const retryLoad = () => {
  loaded.value = false
  error.value = ''
  refreshPage()
}

const tryEmbedAnyway = () => {
  showDirectWarning.value = false
  loaded.value = false
  error.value = ''
}

// Watchers
watch(() => props.url, () => {
  if (props.url) {
    loaded.value = false
    error.value = ''
    loadAttempts.value = 0

    // 检查是否可以使用代理
    const proxyUrl = getProxyUrl(props.url)
    if (proxyUrl) {
      // 可以使用代理，不显示警告
      showDirectWarning.value = false
    } else if (isRestrictedSite(props.url)) {
      // 受限网站且无代理支持
      showDirectWarning.value = true
    } else {
      showDirectWarning.value = false
    }
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  // 设置iframe加载超时
  setTimeout(() => {
    if (!loaded.value && !error.value) {
      handleError()
    }
  }, 15000) // 15秒超时
})
</script>

<style lang="scss" scoped>
.web-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.web-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .toolbar-right {
    .url-display {
      font-size: 12px;
      color: #666;
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.embed-wrapper {
  flex: 1;
  position: relative;
  min-height: 0;
  background: white;
}

.loading-indicator,
.error-indicator,
.restricted-warning {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  color: #666;
  z-index: 10;

  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .loading-spin {
    animation: spin 1s linear infinite;
    color: #409eff;
  }

  p {
    margin: 8px 0;
    font-size: 14px;
  }

  .error-detail {
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
    text-align: center;
    max-width: 300px;
  }

  .error-actions {
    display: flex;
    gap: 8px;
  }
}

.restricted-warning {
  background: #fafbfc;
  border: 1px solid #e1e4e8;

  .el-icon {
    color: #f56c6c;
  }

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #333;
  }

  p {
    margin: 8px 0;
    font-size: 14px;
    text-align: center;
    max-width: 400px;
  }

  .warning-detail {
    font-size: 12px;
    color: #999;
    margin-bottom: 24px;
  }

  .warning-actions {
    display: flex;
    gap: 12px;

    .el-button {
      padding: 12px 24px;
    }
  }
}

.web-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  transition: opacity 0.3s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
