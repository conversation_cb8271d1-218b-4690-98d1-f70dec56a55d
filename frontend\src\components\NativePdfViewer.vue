<template>
  <div class="native-pdf-viewer">
    <div class="pdf-container">
      <!-- 直接使用iframe加载PDF，让浏览器处理 -->
      <iframe
        v-if="pdfUrl"
        :src="pdfUrl"
        class="pdf-iframe"
        frameborder="0"
        allowfullscreen
        title="PDF文档查看器"
      ></iframe>

      <!-- 没有PDF URL时的提示 -->
      <div v-else class="no-pdf">
        <el-icon><Document /></el-icon>
        <p>没有可显示的PDF文档</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document } from "@element-plus/icons-vue";

interface Props {
  pdfUrl?: string;
}

const props = defineProps<Props>();
</script>

<style lang="scss" scoped>
.native-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-container {
  flex: 1;
  position: relative;
  min-height: 0;
  background: white;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.no-pdf {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  color: #666;

  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #999;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}
</style>
