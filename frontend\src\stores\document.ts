import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Document } from '@/types'
import { documentAPI } from '@/utils/api'

export const useDocumentStore = defineStore('document', () => {
  // State
  const documents = ref<Document[]>([])
  const currentDocument = ref<Document | null>(null)
  const selectedText = ref('')
  const loading = ref(false)
  const uploading = ref(false)

  // Getters
  const documentCount = computed(() => documents.value.length)
  const hasDocuments = computed(() => documents.value.length > 0)
  const currentDocumentContent = computed(() => currentDocument.value?.content || '')

  // Actions
  const fetchDocuments = async () => {
    loading.value = true
    try {
      documents.value = await documentAPI.listDocuments()
    } catch (error) {
      console.error('Failed to fetch documents:', error)
      // Don't throw error to avoid uncaught promise rejection
      documents.value = []
    } finally {
      loading.value = false
    }
  }

  const uploadFile = async (file: File) => {
    uploading.value = true
    try {
      const document = await documentAPI.uploadFile(file)
      documents.value.unshift(document)
      return document
    } catch (error) {
      console.error('Failed to upload file:', error)
      throw error
    } finally {
      uploading.value = false
    }
  }

  const uploadUrl = async (url: string, title?: string) => {
    uploading.value = true
    try {
      const document = await documentAPI.uploadUrl(url, title)
      documents.value.unshift(document)
      return document
    } catch (error) {
      console.error('Failed to upload URL:', error)
      throw error
    } finally {
      uploading.value = false
    }
  }

  const selectDocument = async (documentId: number) => {
    try {
      const document = await documentAPI.getDocument(documentId)
      currentDocument.value = document
      return document
    } catch (error) {
      console.error('Failed to select document:', error)
      throw error
    }
  }

  const deleteDocument = async (documentId: number) => {
    try {
      await documentAPI.deleteDocument(documentId)
      documents.value = documents.value.filter(doc => doc.id !== documentId)
      
      if (currentDocument.value?.id === documentId) {
        currentDocument.value = null
      }
    } catch (error) {
      console.error('Failed to delete document:', error)
      throw error
    }
  }

  const setSelectedText = (text: string) => {
    selectedText.value = text
  }

  const clearSelectedText = () => {
    selectedText.value = ''
  }

  const clearCurrentDocument = () => {
    currentDocument.value = null
  }

  return {
    // State
    documents,
    currentDocument,
    selectedText,
    loading,
    uploading,
    
    // Getters
    documentCount,
    hasDocuments,
    currentDocumentContent,
    
    // Actions
    fetchDocuments,
    uploadFile,
    uploadUrl,
    selectDocument,
    deleteDocument,
    setSelectedText,
    clearSelectedText,
    clearCurrentDocument
  }
})
