from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.models.question import Question, AnswerRecord
from app.models.document import Document
from app.services.ai_service import ai_service
from app.services.question_service import question_service

router = APIRouter()

class QuestionResponse(BaseModel):
    id: int
    question_type: str
    difficulty: str
    question_text: str
    options: Optional[List[str]] = None
    choice_mapping: Optional[dict] = None
    incorrect_mermaid: Optional[str] = None
    correct_mermaid: Optional[str] = None
    created_at: str

class QuestionWithAnswer(BaseModel):
    id: int
    question_type: str
    difficulty: str
    question_text: str
    options: Optional[List[str]] = None
    choice_mapping: Optional[dict] = None
    incorrect_mermaid: Optional[str] = None
    correct_mermaid: Optional[str] = None
    correct_answer: str
    explanation: str
    created_at: str

class GenerateQuestionsRequest(BaseModel):
    document_id: Optional[int] = None
    content: Optional[str] = ""
    question_type: str = "choice"  # choice, judgment, or process_error
    difficulty: str = "medium"  # easy, medium, hard
    count: int = 3

class AnswerRequest(BaseModel):
    question_id: int
    user_answer: str
    time_spent: Optional[int] = None

class AnswerResponse(BaseModel):
    is_correct: bool
    correct_answer: str
    explanation: str
    question_id: int

class QuestionStats(BaseModel):
    total_questions: int
    total_answered: int
    correct_answers: int
    accuracy_rate: float

class GenerateQuestionsResponse(BaseModel):
    """返回原始生成的问题JSON格式，与knowledge目录格式一致"""
    questions_data: dict
    saved_questions: List[QuestionResponse]

@router.post("/generate", response_model=GenerateQuestionsResponse)
async def generate_questions(
    request: GenerateQuestionsRequest,
    db: Session = Depends(get_db)
):
    """Generate questions based on document content or provided text"""
    print("🚀 Starting question generation process...")
    try:
        # Get content from document or use provided content
        content = request.content
        document_id = request.document_id

        if request.document_id and not content:
            document = db.query(Document).filter(Document.id == request.document_id).first()
            if not document:
                raise HTTPException(status_code=404, detail="Document not found")
            if not document.content:
                raise HTTPException(status_code=400, detail="Document has no content")
            content = document.content
            document_id = document.id

        if not content:
            raise HTTPException(status_code=400, detail="No content provided for question generation")

        print("🧠 Calling question_service.generate_questions...")
        # Generate questions using new question service
        questions_data = await question_service.generate_questions(
            content=content,
            question_type=request.question_type,
            count=request.count
        )
        print(f"✅ Received from question_service: {questions_data}")

        if "error" in questions_data:
            print(f"❌ Error from question_service: {questions_data['error']}")
            raise HTTPException(status_code=500, detail=questions_data["error"])

        print("🔄 Converting to database format...")
        # Convert to database format
        db_questions = question_service.convert_to_database_format(
            questions_data, request.question_type
        )
        print(f"✅ Converted to db_questions: {db_questions}")

        if not db_questions:
            print("❌ No questions were converted to database format. Aborting.")
            raise HTTPException(status_code=500, detail="Failed to convert any questions to a valid database format.")

        # --- Refactored Database Transaction ---
        newly_added_questions = []
        print("💾 Preparing questions for database (atomic transaction)...")
        for i, q_data in enumerate(db_questions):
            print(f"  - Preparing question {i+1}/{len(db_questions)}: {q_data.get('question_text')[:50]}...")
            question = Question(
                document_id=document_id,
                question_type=request.question_type,
                difficulty=q_data.get("difficulty", "medium"),
                question_text=q_data.get("question_text", ""),
                options=q_data.get("options"),
                choice_mapping=q_data.get("choice_mapping"),
                incorrect_mermaid=q_data.get("incorrect_mermaid"),
                correct_mermaid=q_data.get("correct_mermaid"),
                correct_answer=q_data.get("correct_answer", ""),
                explanation=q_data.get("explanation", ""),
                source_content=""
            )
            db.add(question)
            newly_added_questions.append(question)

        print("  - Committing all questions at once...")
        db.commit()
        print("✅ Transaction committed.")

        for question in newly_added_questions:
            db.refresh(question)
        print("✅ Refreshed question objects to get IDs.")
        # --- End of Refactored Transaction ---

        # Build response
        question_responses = [
            QuestionResponse(
                id=q.id,
                question_type=q.question_type,
                difficulty=q.difficulty,
                question_text=q.question_text,
                options=q.options,
                choice_mapping=q.choice_mapping,
                incorrect_mermaid=q.incorrect_mermaid,
                correct_mermaid=q.correct_mermaid,
                created_at=q.created_at.isoformat()
            )
            for q in newly_added_questions
        ]

        print("🎉 Successfully generated and saved questions. Returning response.")
        return GenerateQuestionsResponse(
            questions_data=questions_data,
            saved_questions=question_responses
        )

    except Exception as e:
        import traceback
        print(f"❌ An unexpected error occurred in generate_questions endpoint: {e}")
        print("--- TRACEBACK ---")
        print(traceback.format_exc())
        print("-----------------")
        db.rollback() # Rollback transaction on error
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred during question generation: {str(e)}")

@router.get("/", response_model=List[QuestionResponse])
async def list_questions(
    document_id: Optional[int] = None,
    question_type: Optional[str] = None,
    difficulty: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """List questions with optional filters"""
    
    query = db.query(Question).filter(Question.is_active == True)
    
    if document_id:
        query = query.filter(Question.document_id == document_id)
    if question_type:
        query = query.filter(Question.question_type == question_type)
    if difficulty:
        query = query.filter(Question.difficulty == difficulty)
    
    questions = query.order_by(Question.created_at.desc()).offset(skip).limit(limit).all()
    
    return [
        QuestionResponse(
            id=q.id,
            question_type=q.question_type,
            difficulty=q.difficulty,
            question_text=q.question_text,
            options=q.options,
            choice_mapping=q.choice_mapping,
            incorrect_mermaid=q.incorrect_mermaid,
            correct_mermaid=q.correct_mermaid,
            created_at=q.created_at.isoformat()
        )
        for q in questions
    ]

@router.post("/answer", response_model=AnswerResponse)
async def submit_answer(
    request: AnswerRequest,
    db: Session = Depends(get_db)
):
    """Submit an answer for a question"""
    
    # Get question
    question = db.query(Question).filter(Question.id == request.question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    # Check if answer is correct
    is_correct = request.user_answer.lower() == question.correct_answer.lower()
    
    # Save answer record
    answer_record = AnswerRecord(
        question_id=request.question_id,
        user_answer=request.user_answer,
        is_correct=is_correct,
        time_spent=request.time_spent
    )
    
    db.add(answer_record)
    db.commit()
    
    return AnswerResponse(
        is_correct=is_correct,
        correct_answer=question.correct_answer,
        explanation=question.explanation or "",
        question_id=question.id
    )

@router.get("/{question_id}", response_model=QuestionWithAnswer)
async def get_question(
    question_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific question with answer (for admin/review purposes)"""
    
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    return QuestionWithAnswer(
        id=question.id,
        question_type=question.question_type,
        difficulty=question.difficulty,
        question_text=question.question_text,
        options=question.options,
        choice_mapping=question.choice_mapping,
        incorrect_mermaid=question.incorrect_mermaid,
        correct_mermaid=question.correct_mermaid,
        correct_answer=question.correct_answer,
        explanation=question.explanation or "",
        created_at=question.created_at.isoformat()
    )

@router.get("/stats/summary", response_model=QuestionStats)
async def get_question_stats(
    db: Session = Depends(get_db)
):
    """Get overall question statistics"""
    
    total_questions = db.query(Question).filter(Question.is_active == True).count()
    total_answered = db.query(AnswerRecord).count()
    correct_answers = db.query(AnswerRecord).filter(AnswerRecord.is_correct == True).count()
    
    accuracy_rate = (correct_answers / total_answered * 100) if total_answered > 0 else 0
    
    return QuestionStats(
        total_questions=total_questions,
        total_answered=total_answered,
        correct_answers=correct_answers,
        accuracy_rate=round(accuracy_rate, 2)
    )

@router.delete("/{question_id}")
async def delete_question(
    question_id: int,
    db: Session = Depends(get_db)
):
    """Delete a question (soft delete by setting is_active to False)"""
    
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    question.is_active = False
    db.commit()
    
    return {"message": "Question deleted successfully"}
