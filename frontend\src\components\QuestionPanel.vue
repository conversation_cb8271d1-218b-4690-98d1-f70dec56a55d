<template>
  <div class="question-panel">
    <!-- Compact Controls Header -->
    <div class="compact-controls">
      <el-row :gutter="8" align="middle">
        <el-col :span="8">
          <el-select v-model="questionType" size="small" style="width: 100%" placeholder="题目类型">
            <el-option label="选择题" value="choice" />
            <el-option label="判断题" value="judgment" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select v-model="difficulty" size="small" style="width: 100%" placeholder="难度">
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <div class="action-buttons">
            <el-button
              size="small"
              @click="generateQuestions"
              :loading="questionStore.generating"
              title="生成题目"
              type="primary"
              :icon="Refresh"
            >
              生成
            </el-button>
            <el-button
              size="small"
              title="设置"
              :icon="Setting"
              @click="handleSettings"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- Questions List -->
    <div class="questions-container">
      <el-scrollbar class="questions-scroll">
        <div v-if="questionStore.generating" class="generating-placeholder">
          <el-icon class="loading-spin"><loading /></el-icon>
          <p>正在生成题目...</p>
        </div>

        <div v-else-if="!questionStore.hasQuestions" class="empty-placeholder">
          <el-empty description="暂无题目">
            <template #image>
              <el-icon size="60"><document-checked /></el-icon>
            </template>
            <el-button type="primary" @click="generateQuestions">
              生成题目
            </el-button>
          </el-empty>
        </div>

        <div v-else class="questions-list">
          <transition-group name="question-card" tag="div">
            <div
              v-for="question in displayQuestions"
              :key="question.id"
              class="question-card"
              :class="{
                'answered': questionStore.isQuestionAnswered(question.id),
                'correct': questionStore.isAnswerCorrect(question.id),
                'breaking': breakingQuestions.has(question.id),
                'sliding-away': slidingAwayQuestions.has(question.id),
                'showing-explanation': showingExplanation.has(question.id),
                'difficulty-easy': question.difficulty === 'easy',
                'difficulty-medium': question.difficulty === 'medium',
                'difficulty-hard': question.difficulty === 'hard'
              }"
            >
              <div class="question-content">
                <!-- 正常问题显示 -->
                <div v-if="!showingExplanation.has(question.id)" class="normal-question">
                  <div class="question-header">
                    <div class="question-text">
                      {{ question.question_text }}
                    </div>
                    <el-button
                      size="small"
                      type="text"
                      @click="handleAIAnalysis(question)"
                      class="ai-analysis-btn"
                      title="让AI解析这道题目"
                    >
                      <el-icon><ChatLineSquare /></el-icon>
                      AI解析
                    </el-button>
                  </div>

                  <div v-if="!questionStore.isQuestionAnswered(question.id)" class="question-options">
                  <!-- Choice Question Options -->
                  <div v-if="question.question_type === 'choice' && question.options">
                    <el-radio-group
                      v-model="selectedAnswers[question.id]"
                      @change="handleAnswerSelect(question, $event)"
                      class="options-group"
                    >
                      <div
                        v-for="(option, index) in question.options"
                        :key="index"
                        class="option-item"
                      >
                        <el-radio :label="getOptionLabel(index)" class="option-radio">
                          <span class="option-content">
                            <span class="option-label">{{ getOptionLabel(index) }}.</span>
                            <span class="option-text">{{ option }}</span>
                          </span>
                        </el-radio>
                      </div>
                    </el-radio-group>
                  </div>

                  <!-- Judgment Question Options -->
                  <div v-else-if="question.question_type === 'judgment'">
                    <el-radio-group
                      v-model="selectedAnswers[question.id]"
                      @change="handleAnswerSelect(question, $event)"
                      class="options-group judgment-options"
                    >
                      <div class="option-item">
                        <el-radio label="true" class="option-radio">
                          <span class="option-content">
                            <span class="option-text">正确</span>
                          </span>
                        </el-radio>
                      </div>
                      <div class="option-item">
                        <el-radio label="false" class="option-radio">
                          <span class="option-content">
                            <span class="option-text">错误</span>
                          </span>
                        </el-radio>
                      </div>
                    </el-radio-group>
                  </div>
                </div>
                </div>

                <!-- 解析显示 -->
                <div v-if="showingExplanation.has(question.id)" class="explanation-display">
                  <div class="question-text">
                    {{ question.question_text }}
                  </div>

                  <div v-if="answerResults[question.id]" class="answer-result explanation-result"
                       :class="{ 'correct': answerResults[question.id].is_correct }">
                    <div class="result-header">
                      <el-icon v-if="answerResults[question.id].is_correct" class="success-icon">
                        <check />
                      </el-icon>
                      <el-icon v-else class="error-icon">
                        <close />
                      </el-icon>
                      <span class="result-text">
                        {{ answerResults[question.id].is_correct ? '回答正确！' : '回答错误' }}
                      </span>
                    </div>

                    <div v-if="!answerResults[question.id].is_correct" class="correct-answer">
                      正确答案: {{ answerResults[question.id].correct_answer }}
                    </div>

                    <div v-if="answerResults[question.id].explanation" class="explanation">
                      <strong>解析:</strong> {{ answerResults[question.id].explanation }}
                    </div>
                  </div>
                </div>

                <!-- Answer Result -->
                <div 
                  v-if="answerResults[question.id]" 
                  class="answer-result"
                  :class="{ 'correct': answerResults[question.id].is_correct }"
                >
                  <div class="result-header">
                    <el-icon v-if="answerResults[question.id].is_correct" class="success-icon">
                      <check />
                    </el-icon>
                    <el-icon v-else class="error-icon">
                      <close />
                    </el-icon>
                    <span class="result-text">
                      {{ answerResults[question.id].is_correct ? '回答正确！' : '回答错误' }}
                    </span>
                  </div>
                  
                  <div v-if="!answerResults[question.id].is_correct" class="correct-answer">
                    正确答案: {{ answerResults[question.id].correct_answer }}
                  </div>
                  
                  <div v-if="answerResults[question.id].explanation" class="explanation">
                    <strong>解析:</strong> {{ answerResults[question.id].explanation }}
                  </div>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Setting,
  Loading,
  DocumentChecked,
  Check,
  Close,
  ChatLineSquare
} from '@element-plus/icons-vue'
import { useQuestionStore } from '@/stores/question'
import { useDocumentStore } from '@/stores/document'
import { useChatStore } from '@/stores/chat'
import { questionsAPI } from '@/utils/api'
import type { Question, AnswerResponse } from '@/types'

// Props
interface Props {
  documentId?: number
}

const props = withDefaults(defineProps<Props>(), {
  documentId: undefined
})

// Stores
const questionStore = useQuestionStore()
const documentStore = useDocumentStore()
const chatStore = useChatStore()

// State
const questionType = ref<'choice' | 'judgment'>('choice')
const difficulty = ref<'easy' | 'medium' | 'hard'>('medium')
const selectedAnswers = reactive<Record<number, string>>({})
const answerResults = reactive<Record<number, AnswerResponse>>({})
const breakingQuestions = ref<Set<number>>(new Set())
const showingExplanation = ref<Set<number>>(new Set())
const slidingAwayQuestions = ref<Set<number>>(new Set())
// Computed
const displayQuestions = computed(() => {
  return questionStore.unansweredQuestions.slice(0, 5) // Show max 5 questions
})

// Methods
const generateQuestions = async () => {
  try {
    let content = ''

    if (props.documentId) {
      const document = documentStore.documents.find(d => d.id === props.documentId)
      content = document?.content || ''
    } else if (documentStore.currentDocument) {
      content = documentStore.currentDocument.content || ''
    }

    if (!content && !props.documentId) {
      ElMessage.warning('请先选择文档或上传内容')
      return
    }

    // 直接使用 axios 调用，确保超时设置生效
    questionStore.generating = true

    try {
      const response = await questionsAPI.generateQuestions({
        document_id: props.documentId,
        content,
        question_type: questionType.value,
        difficulty: difficulty.value,
        count: 3
      })

      // 手动更新问题列表
      questionStore.questions.unshift(...response.saved_questions)
      ElMessage.success('题目生成成功')
    } finally {
      questionStore.generating = false
    }
  } catch (error) {
    ElMessage.error('题目生成失败')
    console.error('Generate questions error:', error)
  }
}

const handleAnswerSelect = async (question: Question, answer: string) => {
  const startTime = Date.now()

  try {
    const response = await questionStore.submitAnswer(
      question.id,
      answer,
      Math.floor((Date.now() - startTime) / 1000)
    )

    answerResults[question.id] = response

    if (response.is_correct) {
      // 答对：立即显示解析0.5秒，然后触发破碎动画
      showingExplanation.value.add(question.id)

      setTimeout(() => {
        // 隐藏解析，开始破碎动画
        showingExplanation.value.delete(question.id)
        breakingQuestions.value.add(question.id)

        setTimeout(() => {
          // 破碎动画完成后清理
          questionStore.removeQuestion(question.id)
          breakingQuestions.value.delete(question.id)
          delete selectedAnswers[question.id]
          delete answerResults[question.id]
        }, 600) // 破碎动画时间
      }, 500) // 显示解析0.5秒

      ElMessage.success('回答正确！')
    } else {
      // 答错：先触发滑走动画，然后在原位置显示解析3秒
      slidingAwayQuestions.value.add(question.id)

      setTimeout(() => {
        // 滑走动画完成，显示解析
        slidingAwayQuestions.value.delete(question.id)
        showingExplanation.value.add(question.id)

        setTimeout(() => {
          // 解析显示3秒后清理
          showingExplanation.value.delete(question.id)
          questionStore.removeQuestion(question.id)
          delete selectedAnswers[question.id]
          delete answerResults[question.id]
        }, 3000) // 显示解析3秒
      }, 300) // 滑走动画时间

      ElMessage.error('回答错误，请查看解析')
    }
  } catch (error) {
    ElMessage.error('提交答案失败')
    console.error('Submit answer error:', error)
  }
}

const handleSettings = () => {
  ElMessage.info('设置功能开发中...')
}

const handleAIAnalysis = async (question: Question) => {
  try {
    // 构建问题文本，包含选项
    let questionText = `请帮我分析这道题目：\n\n${question.question_text}\n\n`

    if (question.question_type === 'choice' && question.options) {
      questionText += '选项：\n'
      question.options.forEach((option, index) => {
        const label = String.fromCharCode(65 + index) // A, B, C, D
        questionText += `${label}. ${option}\n`
      })
    } else if (question.question_type === 'judgment') {
      questionText += '这是一道判断题，请判断正确或错误。\n'
    }

    questionText += '\n请提供详细的解析和答案。'

    // 发送消息到聊天界面
    await chatStore.sendMessage(
      questionText,
      props.documentId, // 传递文档ID作为上下文
      undefined, // 没有选中文本
      true, // 使用流式传输
      {
        mode: 'normal',
        thinking: 'detailed',
        prompt: 'analysis'
      }
    )

    ElMessage.success('问题已发送到聊天界面，请查看AI解析')
  } catch (error) {
    ElMessage.error('发送AI解析请求失败')
    console.error('AI analysis error:', error)
  }
}

const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index) // A, B, C, D
}

// Lifecycle
onMounted(async () => {
  try {
    if (props.documentId) {
      await questionStore.fetchQuestions({ document_id: props.documentId })
    }
    await questionStore.fetchStats()
  } catch (error) {
    // Errors are already handled in store
  }
})
</script>

<style lang="scss" scoped>
@use "sass:color";
.question-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $panel-right-bg;
}

.compact-controls {
  padding: $spacing-xs $spacing-sm;
  border-bottom: 1px solid $border-color-lighter;
  background: $bg-color-lighter;

  .action-buttons {
    display: flex;
    gap: $spacing-xs;
    justify-content: flex-end;

    .el-button {
      min-width: auto;
    }
  }
}

.questions-container {
  flex: 1;
  min-height: 0;
}

.questions-scroll {
  height: 100%;
}

.questions-list {
  padding: $spacing-sm;
}

.question-card {
  background: $question-card-bg;
  border: 1px solid $question-card-border;
  border-radius: $border-radius-md;
  margin-bottom: $spacing-sm;
  box-shadow: $question-card-shadow;
  transition: $transition-base;
  overflow: hidden;
  
  &.answered {
    opacity: 0.8;
  }
  
  &.correct {
    border-color: $success-color;
    background: color.adjust($success-color, $lightness: 45%);
  }
  
  &.breaking {
    animation: cardBreak 0.6s ease-in-out forwards;
  }

  &.sliding-away {
    animation: slideAway 0.3s ease-in forwards;
  }

  &.showing-explanation {
    .explanation-display {
      animation: fadeInExplanation 0.2s ease-out;
    }
  }

  // 困难程度颜色标记
  &.difficulty-easy {
    border-left: 4px solid $success-color;
    background: linear-gradient(135deg, rgba($success-color, 0.08) 0%, rgba($success-color, 0.03) 100%);

    .question-text {
      background: rgba($success-color, 0.06);
      border-color: rgba($success-color, 0.15);
    }

    &:hover {
      background: linear-gradient(135deg, rgba($success-color, 0.12) 0%, rgba($success-color, 0.05) 100%);
      box-shadow: 0 4px 20px rgba($success-color, 0.15);

      .question-text {
        background: rgba($success-color, 0.08);
      }
    }
  }

  &.difficulty-medium {
    border-left: 4px solid $warning-color;
    background: linear-gradient(135deg, rgba($warning-color, 0.08) 0%, rgba($warning-color, 0.03) 100%);

    .question-text {
      background: rgba($warning-color, 0.06);
      border-color: rgba($warning-color, 0.15);
    }

    &:hover {
      background: linear-gradient(135deg, rgba($warning-color, 0.12) 0%, rgba($warning-color, 0.05) 100%);
      box-shadow: 0 4px 20px rgba($warning-color, 0.15);

      .question-text {
        background: rgba($warning-color, 0.08);
      }
    }
  }

  &.difficulty-hard {
    border-left: 4px solid $danger-color;
    background: linear-gradient(135deg, rgba($danger-color, 0.08) 0%, rgba($danger-color, 0.03) 100%);

    .question-text {
      background: rgba($danger-color, 0.06);
      border-color: rgba($danger-color, 0.15);
    }

    &:hover {
      background: linear-gradient(135deg, rgba($danger-color, 0.12) 0%, rgba($danger-color, 0.05) 100%);
      box-shadow: 0 4px 20px rgba($danger-color, 0.15);

      .question-text {
        background: rgba($danger-color, 0.08);
      }
    }
  }
  
  .question-content {
    padding: $spacing-sm $spacing-md;

    .question-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: $spacing-sm;
      margin-bottom: $spacing-sm;

      .question-text {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
        color: $text-color-primary;
        font-weight: 600;
        margin-bottom: 0;
        padding: $spacing-sm;
        background: rgba($text-color-primary, 0.03);
        border-radius: $border-radius-sm;
        border: 1px solid rgba($text-color-primary, 0.08);
      }

      .ai-analysis-btn {
        flex-shrink: 0;
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
        color: $primary-color;
        border: 1px solid rgba($primary-color, 0.3);
        border-radius: $border-radius-small;
        background: rgba($primary-color, 0.05);
        transition: all 0.2s ease;

        &:hover {
          background: rgba($primary-color, 0.1);
          border-color: rgba($primary-color, 0.5);
          color: $primary-color;
        }

        .el-icon {
          margin-right: 4px;
          font-size: 12px;
        }
      }
    }
    
    .question-options {
      // el-radio-group 是Element Plus组件，直接对它应用grid样式
      :deep(.el-radio-group.options-group) {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: $spacing-xs;
        width: 100%;

        // 选择题使用2列布局
        &:not(.judgment-options) {
          grid-template-columns: 1fr 1fr !important;
        }

        // 判断题保持原有的2列布局
        &.judgment-options {
          grid-template-columns: 1fr 1fr !important;
        }
      }

      .option-item {
        position: relative;
        border-radius: $border-radius-md;
        transition: $transition-base;
        border: 2px solid transparent;
        width: 100%;
        box-sizing: border-box;

        &:hover {
          background-color: $bg-color-light;
          transform: translateX(2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateX(1px) scale(0.98);
        }

        .option-radio {
          width: 100%;
          margin: 0;
          padding: $spacing-sm;
          border-radius: $border-radius-sm;
          transition: $transition-base;

          &:hover {
            background-color: rgba($primary-color, 0.05);
            box-shadow: 0 2px 8px rgba($primary-color, 0.15);
          }

          &:active {
            transform: scale(0.98);
          }

          :deep(.el-radio__input) {
            margin-right: 0;

            // 隐藏单选按钮的圆圈
            .el-radio__inner {
              display: none !important;
            }

            &.is-checked {
              .el-radio__inner {
                display: none !important;
              }
            }
          }

          :deep(.el-radio__label) {
            width: 100%;
            padding: 0;
            color: $text-color-regular;
            font-size: 14px;
            line-height: 1.5;

            .option-content {
              display: flex;
              align-items: flex-start;
              width: 100%;

              .option-label {
                color: $primary-color;
                font-weight: 600;
                margin-right: $spacing-sm;
                min-width: 20px;
                flex-shrink: 0;
                position: relative;
              }

              .option-text {
                flex: 1;
                white-space: normal;
                word-break: break-word;
                line-height: 1.6;
              }
            }
          }

          // 选中状态的整体样式
          :deep(.el-radio__input.is-checked) ~ .el-radio__label {
            color: $primary-color;
            font-weight: 500;

            .option-content {
              .option-label {
                color: $primary-color;
                font-weight: 700;

                // 添加勾选标记
                &::after {
                  content: '✓';
                  position: absolute;
                  right: -8px;
                  top: 0;
                  color: $primary-color;
                  font-weight: bold;
                  font-size: 12px;
                }
              }

              .option-text {
                color: $text-color-primary;
                font-weight: 500;
              }
            }
          }
        }

        // 选中状态的外层容器样式
        &:has(.el-radio__input.is-checked) {
          border-color: $primary-color;
          background-color: rgba($primary-color, 0.1);
          box-shadow: 0 0 0 2px rgba($primary-color, 0.2);

          .option-radio {
            background-color: rgba($primary-color, 0.05);
          }
        }
      }

      // 判断题特殊样式
      &.judgment-options {
        gap: $spacing-md;

        .option-item {
          .option-radio {
            text-align: center;

            // 隐藏判断题的单选按钮圆圈
            :deep(.el-radio__input) {
              margin-right: 0;

              .el-radio__inner {
                display: none !important;
              }

              &.is-checked {
                .el-radio__inner {
                  display: none !important;
                }
              }
            }

            &:hover {
              background-color: rgba($success-color, 0.05);
              box-shadow: 0 2px 8px rgba($success-color, 0.15);
            }

            &:first-child:hover {
              background-color: rgba($success-color, 0.05);
            }

            &:last-child:hover {
              background-color: rgba($danger-color, 0.05);
              box-shadow: 0 2px 8px rgba($danger-color, 0.15);
            }

            :deep(.el-radio__label) {
              .option-content {
                justify-content: center;

                .option-text {
                  text-align: center;
                  font-weight: 500;
                  font-size: 15px;
                  transition: $transition-color;
                }
              }
            }

            // 正确选项的特殊样式
            &:first-child {
              :deep(.el-radio__input.is-checked) ~ .el-radio__label {
                color: $success-color;

                .option-content {
                  .option-text {
                    color: $success-color;
                    font-weight: 600;
                    position: relative;

                    // 添加勾选标记
                    &::before {
                      content: '✓';
                      position: absolute;
                      left: -20px;
                      top: 0;
                      color: $success-color;
                      font-weight: bold;
                      font-size: 16px;
                    }
                  }
                }
              }
            }

            // 错误选项的特殊样式
            &:last-child {
              :deep(.el-radio__input.is-checked) ~ .el-radio__label {
                color: $danger-color;

                .option-content {
                  .option-text {
                    color: $danger-color;
                    font-weight: 600;
                    position: relative;

                    // 添加勾选标记
                    &::before {
                      content: '✓';
                      position: absolute;
                      left: -20px;
                      top: 0;
                      color: $danger-color;
                      font-weight: bold;
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }

          // 判断题选中状态的外层样式
          &:first-child:has(.el-radio__input.is-checked) {
            border-color: $success-color;
            background-color: rgba($success-color, 0.1);
            box-shadow: 0 0 0 2px rgba($success-color, 0.2);

            .option-radio {
              background-color: rgba($success-color, 0.08);
            }
          }

          &:last-child:has(.el-radio__input.is-checked) {
            border-color: $danger-color;
            background-color: rgba($danger-color, 0.1);
            box-shadow: 0 0 0 2px rgba($danger-color, 0.2);

            .option-radio {
              background-color: rgba($danger-color, 0.08);
            }
          }
        }
      }
    }
    
    .answer-result {
      margin-top: $spacing-sm;
      padding: $spacing-sm;
      border-radius: $border-radius-sm;
      background: color.adjust($danger-color, $lightness: 40%);
      border: 1px solid color.adjust($danger-color, $lightness: 20%);

      &.correct {
        background: color.adjust($success-color, $lightness: 40%);
        border-color: color.adjust($success-color, $lightness: 20%);
      }
      
      .result-header {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-sm;
        
        .success-icon {
          color: $success-color;
          margin-right: $spacing-sm;
        }
        
        .error-icon {
          color: $danger-color;
          margin-right: $spacing-sm;
        }
        
        .result-text {
          font-weight: 500;
          font-size: 14px;
        }
      }
      
      .correct-answer {
        font-size: 13px;
        color: $text-color-regular;
        margin-bottom: $spacing-sm;
        font-weight: 500;
      }
      
      .explanation {
        font-size: 13px;
        color: $text-color-regular;
        line-height: 1.5;
        
        strong {
          color: $text-color-primary;
        }
      }
    }
  }

  .explanation-display {
    .question-text {
      font-size: 14px;
      line-height: 1.4;
      color: $text-color-primary;
      margin-bottom: $spacing-sm;
      font-weight: 600;
      padding: $spacing-sm;
      background: rgba($text-color-primary, 0.03);
      border-radius: $border-radius-sm;
      border: 1px solid rgba($text-color-primary, 0.08);
    }

    .explanation-result {
      margin-top: $spacing-sm;
    }
  }
}

.generating-placeholder,
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: $text-color-secondary;
  
  .el-icon {
    font-size: 24px;
    margin-bottom: $spacing-md;
  }
}

// Animations
.question-card-enter-active {
  transition: all 0.3s ease-out;
}

.question-card-leave-active {
  transition: all 0.3s ease-in;
}

.question-card-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.question-card-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@keyframes cardBreak {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(0) rotate(10deg);
    opacity: 0;
  }
}

@keyframes slideAway {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeInExplanation {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes optionHover {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(2px);
  }
}

@keyframes optionSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba($primary-color, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .question-options {
    // 小屏幕上选择题改为单列布局
    :deep(.el-radio-group.options-group:not(.judgment-options)) {
      grid-template-columns: 1fr !important;
    }

    .option-item {
      .option-radio {
        padding: $spacing-xs;

        :deep(.el-radio__label) {
          .option-content {
            .option-label {
              min-width: 18px;
              font-size: 13px;
            }

            .option-text {
              font-size: 13px;
            }
          }
        }
      }
    }

    &.judgment-options {
      gap: $spacing-xs;

      .option-item .option-radio {
        :deep(.el-radio__label) {
          .option-content .option-text {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 中等屏幕优化
@media (max-width: 1024px) and (min-width: 769px) {
  .question-options {
    :deep(.el-radio-group.options-group) {
      gap: $spacing-xs;
    }

    .option-item {
      .option-radio {
        padding: $spacing-xs $spacing-sm;
      }
    }
  }
}
</style>
