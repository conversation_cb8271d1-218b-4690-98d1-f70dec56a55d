# Knowledge Assistant

AI-powered knowledge learning assistant with document browsing, intelligent Q&A, and adaptive testing.

## 🌟 Features

- **📚 Knowledge Browser**: Upload and view PDF, Word, TXT files and web pages
- **🤖 AI Q&A**: Intelligent conversation powered by DeepSeek API
- **📝 Adaptive Testing**: Auto-generated questions based on learning content
- **🎨 Responsive UI**: Three-panel layout with drag-to-resize functionality

## 🛠 Tech Stack

### Backend
- **FastAPI**: Modern, fast web framework
- **LangChain**: AI application framework
- **DeepSeek API**: Large language model
- **SQLite**: Lightweight database

### Frontend
- **Vue 3**: Progressive JavaScript framework
- **TypeScript**: Type-safe development
- **Element Plus**: Vue 3 UI library
- **Pinia**: State management
- **Vite**: Build tool

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- DeepSeek API Key (already configured)

### Option 1: One-Click Start (Recommended)

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
start.bat
```

### Option 2: Manual Setup

**Backend Setup:**
```bash
cd backend
pip install -r requirements.txt
python main.py
```

**Frontend Setup:**
```bash
cd frontend
npm install
npm run dev
```

### Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📁 Project Structure

```
knowledge_assistant/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── models/         # Database models
│   │   └── services/       # Business logic
│   ├── requirements.txt
│   └── main.py
├── frontend/               # Vue 3 frontend
│   ├── src/
│   │   ├── components/     # Vue components
│   │   ├── views/          # Page views
│   │   ├── stores/         # Pinia stores
│   │   └── utils/          # Utilities
│   ├── package.json
│   └── vite.config.ts
├── docs/                   # Documentation
└── README.md
```

## 🔧 Configuration

Create `.env` file in backend directory:
```
DEEPSEEK_API_KEY=your_api_key_here
DATABASE_URL=sqlite:///./knowledge_assistant.db
```

## 📖 API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [DeepSeek](https://www.deepseek.com/) for providing the AI API
- [Element Plus](https://element-plus.org/) for the beautiful UI components
- [LangChain](https://langchain.com/) for the AI framework
