#!/usr/bin/env python3
"""
创建一个测试PDF文件
"""

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    import os
    
    def create_test_pdf():
        # 创建PDF文件
        filename = "test_document.pdf"
        c = canvas.Canvas(filename, pagesize=letter)
        
        # 添加标题
        c.setFont("Helvetica-Bold", 16)
        c.drawString(100, 750, "Knowledge Assistant Test PDF")
        
        # 添加内容
        c.setFont("Helvetica", 12)
        y = 700
        lines = [
            "This is a test PDF document for Knowledge Assistant.",
            "",
            "Features to test:",
            "1. PDF viewing in browser",
            "2. Download functionality", 
            "3. Open in new window",
            "4. Error handling",
            "",
            "This PDF should display correctly in the embedded viewer.",
            "If you can see this text, the PDF viewer is working properly.",
            "",
            "Knowledge Assistant supports multiple document formats:",
            "- PDF files (like this one)",
            "- Word documents (.docx)",
            "- Text files (.txt)",
            "- Web pages (URLs)",
            "",
            "Each format has its own optimized viewer for the best",
            "user experience."
        ]
        
        for line in lines:
            c.drawString(100, y, line)
            y -= 20
        
        # 保存PDF
        c.save()
        print(f"Created {filename}")
        return filename
    
    if __name__ == "__main__":
        create_test_pdf()
        
except ImportError:
    print("reportlab not installed. Creating a simple HTML file instead.")
    
    # 创建一个HTML文件作为替代
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Document</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1 { color: #333; }
            p { line-height: 1.6; }
        </style>
    </head>
    <body>
        <h1>Knowledge Assistant Test Document</h1>
        <p>This is a test document for Knowledge Assistant.</p>
        <h2>Features to test:</h2>
        <ul>
            <li>Document viewing</li>
            <li>Content display</li>
            <li>Error handling</li>
        </ul>
        <p>This document should display correctly in the viewer.</p>
    </body>
    </html>
    """
    
    with open("test_document.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    print("Created test_document.html")
