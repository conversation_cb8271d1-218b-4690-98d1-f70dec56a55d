{"name": "knowledge-assistant-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "marked": "^9.1.6", "pdfjs-dist": "^3.11.174", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.9.0", "@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "sass": "^1.70.0", "typescript": "~5.2.2", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}}