from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import Response, HTMLResponse
import httpx
import re
from urllib.parse import urljoin, urlparse
from typing import Optional

router = APIRouter()

# 支持代理的网站配置
PROXY_SITES = {
    'vue-docs': {
        'target': 'https://cn.vuejs.org',
        'host': 'cn.vuejs.org'
    },
    'react-docs': {
        'target': 'https://react.dev',
        'host': 'react.dev'
    },
    'github': {
        'target': 'https://github.com',
        'host': 'github.com'
    },
    'stackoverflow': {
        'target': 'https://stackoverflow.com',
        'host': 'stackoverflow.com'
    }
}

# 需要移除的安全头
SECURITY_HEADERS_TO_REMOVE = [
    'x-frame-options',
    'content-security-policy',
    'x-content-type-options',
    'x-xss-protection',
    'strict-transport-security'
]

async def get_httpx_client():
    """获取HTTP客户端"""
    return httpx.AsyncClient(
        timeout=30.0,
        follow_redirects=True,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    )

def modify_html_content(content: str, site_key: str, path: str) -> str:
    """修改HTML内容，替换相对路径为代理路径"""
    if not content:
        return content

    site_config = PROXY_SITES.get(site_key)
    if not site_config:
        return content

    target_domain = urlparse(site_config['target']).netloc
    proxy_base = f"http://127.0.0.1:8000/api/proxy/{site_key}"
    api_base = "http://127.0.0.1:8000/api"

    # 替换相对路径 - 更精确的匹配
    content = re.sub(r'href="/', f'href="{proxy_base}/', content)
    content = re.sub(r'src="/', f'src="{proxy_base}/', content)
    content = re.sub(r'action="/', f'action="{proxy_base}/', content)

    # 处理特殊的静态资源路径
    content = re.sub(r'href="/assets/', f'href="{api_base}/assets/', content)
    content = re.sub(r'src="/assets/', f'src="{api_base}/assets/', content)
    content = re.sub(r'href="/vp-icons\.css"', f'href="{api_base}/vp-icons.css"', content)

    # 替换绝对路径
    content = re.sub(f'href="https://{target_domain}', f'href="{proxy_base}', content)
    content = re.sub(f'src="https://{target_domain}', f'src="{proxy_base}', content)

    # 处理JavaScript中的路径引用
    content = re.sub(r'"/assets/', f'"{api_base}/assets/', content)
    content = re.sub(r"'/assets/", f"'{api_base}/assets/", content)

    # 添加base标签以确保相对路径正确
    base_tag = f'<base href="{proxy_base}/">'
    if '<head>' in content:
        content = content.replace('<head>', f'<head>\n{base_tag}')

    return content

@router.get("/proxy/{site_key}/{path:path}")
async def proxy_request(site_key: str, path: str, request: Request):
    """代理请求到目标网站"""
    
    # 检查是否支持该网站
    if site_key not in PROXY_SITES:
        raise HTTPException(status_code=404, detail=f"Proxy site '{site_key}' not supported")
    
    site_config = PROXY_SITES[site_key]
    target_url = f"{site_config['target']}/{path}"
    
    # 获取查询参数
    query_params = str(request.url.query)
    if query_params:
        target_url += f"?{query_params}"
    
    try:
        print(f"Proxying request: {target_url}")  # 添加日志

        async with await get_httpx_client() as client:
            # 构建请求头
            headers = {
                'Host': site_config['host'],
                'Accept': request.headers.get('accept', '*/*'),
                'Accept-Language': request.headers.get('accept-language', 'zh-CN,zh;q=0.9,en;q=0.8'),
                'Accept-Encoding': 'gzip, deflate, br',
                'Referer': site_config['target'],
                'User-Agent': request.headers.get('user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            }
            
            # 发送请求
            response = await client.get(target_url, headers=headers)
            
            # 构建响应头，移除安全限制头
            response_headers = {}
            for key, value in response.headers.items():
                if key.lower() not in SECURITY_HEADERS_TO_REMOVE:
                    response_headers[key] = value
            
            # 添加允许嵌入的头
            response_headers['X-Frame-Options'] = 'ALLOWALL'
            response_headers['Content-Security-Policy'] = "frame-ancestors *;"
            
            # 处理内容类型
            content_type = response.headers.get('content-type', '')
            
            if 'text/html' in content_type:
                # 对HTML内容进行处理
                content = response.text
                modified_content = modify_html_content(content, site_key, path)
                
                return HTMLResponse(
                    content=modified_content,
                    status_code=response.status_code,
                    headers=response_headers
                )
            else:
                # 对于非HTML内容（CSS、JS、图片等），直接返回
                return Response(
                    content=response.content,
                    status_code=response.status_code,
                    headers=response_headers,
                    media_type=content_type
                )
                
    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Proxy request failed: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/proxy/{site_key}")
async def proxy_root(site_key: str, request: Request):
    """代理根路径请求"""
    return await proxy_request(site_key, "", request)

# 通用静态资源代理路由
@router.get("/assets/{path:path}")
async def proxy_assets(path: str, request: Request):
    """代理assets静态资源请求"""
    return await proxy_request("vue-docs", f"assets/{path}", request)

@router.get("/vp-icons.css")
async def proxy_vp_icons(request: Request):
    """代理VitePress图标CSS"""
    return await proxy_request("vue-docs", "vp-icons.css", request)

# 处理其他常见的静态资源
@router.get("/{filename:path}.css")
async def proxy_css_files(filename: str, request: Request):
    """代理CSS文件"""
    if not filename.startswith(('proxy/', 'api/')):
        return await proxy_request("vue-docs", f"{filename}.css", request)
    raise HTTPException(status_code=404, detail="Not found")

@router.get("/{filename:path}.js")
async def proxy_js_files(filename: str, request: Request):
    """代理JS文件"""
    if not filename.startswith(('proxy/', 'api/')):
        return await proxy_request("vue-docs", f"{filename}.js", request)
    raise HTTPException(status_code=404, detail="Not found")

@router.get("/{filename:path}.{ext}")
async def proxy_static_files(filename: str, ext: str, request: Request):
    """代理其他静态文件（图片、字体等）"""
    static_extensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'woff', 'woff2', 'ttf', 'eot']
    if ext.lower() in static_extensions and not filename.startswith(('proxy/', 'api/')):
        return await proxy_request("vue-docs", f"{filename}.{ext}", request)
    raise HTTPException(status_code=404, detail="Not found")

@router.get("/proxy-sites")
async def get_supported_sites():
    """获取支持的代理网站列表"""
    return {
        "supported_sites": {
            key: {
                "name": key.replace('-', ' ').title(),
                "target": config['target'],
                "proxy_url": f"/api/proxy/{key}"
            }
            for key, config in PROXY_SITES.items()
        }
    }
