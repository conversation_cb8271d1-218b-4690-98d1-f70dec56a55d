#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Element Plus Icons 中实际存在的图标列表
const validIcons = [
  'Plus', 'ChatLineSquare', 'Delete', 'ChatDotRound', 'User', 'Service',
  'UploadFilled', 'Document', 'EditPen', 'DocumentCopy', 'Link', 'Refresh',
  'Close', 'Loading', 'Setting', 'DocumentChecked', 'Check', 'ArrowLeft',
  'ArrowRight', 'ArrowUp', 'ArrowDown'
];

// 图标替换映射
const iconReplacements = {
  'Robot': 'Service',
  'robot': 'service'
};

// 需要检查的文件
const filesToCheck = [
  'src/components/ChatInterface.vue',
  'src/components/DocumentBrowser.vue',
  'src/components/QuestionPanel.vue',
  'src/views/HomeView.vue'
];

function fixIconsInFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let hasChanges = false;

  // 替换图标名称
  Object.entries(iconReplacements).forEach(([oldIcon, newIcon]) => {
    const oldPattern = new RegExp(`<${oldIcon}\\s*/>`, 'g');
    const newPattern = `<${newIcon} />`;
    
    if (content.includes(`<${oldIcon}`)) {
      content = content.replace(oldPattern, newPattern);
      hasChanges = true;
      console.log(`在 ${filePath} 中将 ${oldIcon} 替换为 ${newIcon}`);
    }
  });

  if (hasChanges) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`已修复: ${filePath}`);
  }
}

// 执行修复
console.log('开始修复图标问题...');

filesToCheck.forEach(fixIconsInFile);

console.log('\n图标修复完成！');
console.log('\n常用的 Element Plus 图标：');
validIcons.forEach(icon => {
  console.log(`- ${icon}`);
});

console.log('\n如果需要其他图标，请查看：');
console.log('https://element-plus.org/zh-CN/component/icon.html');
