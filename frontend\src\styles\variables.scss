@use "sass:color";

// Color Variables
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// Background Colors
$bg-color: #ffffff;
$bg-color-light: #f5f7fa;
$bg-color-lighter: #fafbfc;
$bg-color-dark: #f0f2f5;

// Text Colors
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// Border Colors
$border-color: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// Layout
$header-height: 48px;
$sidebar-width: 240px;
$panel-min-width: 200px;
$resize-handle-width: 4px;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// Shadows
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);

// Transitions
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-border: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-color: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

// Z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// Panel specific colors
$panel-left-bg: #fafbfc;
$panel-middle-bg: #ffffff;
$panel-right-bg: #f9f9f9;

// Question card colors
$question-card-bg: #ffffff;
$question-card-border: #e4e7ed;
$question-card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// Chat colors
$chat-user-bg: #409eff;
$chat-assistant-bg: #f0f2f5;
$chat-user-text: #ffffff;
$chat-assistant-text: #303133;

// Document colors
$document-item-bg: #ffffff;
$document-item-hover-bg: #f5f7fa;
$document-selected-bg: #ecf5ff;
$document-selected-border: #409eff;
