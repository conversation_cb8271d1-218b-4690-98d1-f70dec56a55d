<template>
  <div class="embed-pdf-viewer">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <span class="pdf-title">PDF 文档查看器</span>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="downloadPdf">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button size="small" @click="openInNewTab" type="primary">
          <el-icon><Link /></el-icon>
          新窗口
        </el-button>
      </div>
    </div>
    
    <div class="pdf-container">
      <div class="pdf-loading" v-if="loading">
        <el-icon class="loading-spin"><Loading /></el-icon>
        <p>正在加载PDF文档...</p>
      </div>
      
      <div class="pdf-error" v-else-if="error">
        <el-icon><Warning /></el-icon>
        <p>PDF显示失败</p>
        <p class="error-detail">{{ error }}</p>
        <div class="error-actions">
          <el-button @click="downloadPdf" type="primary">下载PDF</el-button>
          <el-button @click="openInNewTab">新窗口打开</el-button>
        </div>
      </div>
      
      <!-- 使用embed标签显示PDF -->
      <embed 
        v-else-if="pdfUrl"
        :src="pdfUrl"
        type="application/pdf"
        class="pdf-embed"
        @load="handleLoad"
        @error="handleError"
      />
      
      <!-- 如果没有PDF URL，显示提示 -->
      <div v-else class="no-pdf">
        <el-icon><Document /></el-icon>
        <p>没有可显示的PDF文档</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Link, 
  Loading,
  Warning,
  Download,
  Document
} from '@element-plus/icons-vue'

interface Props {
  pdfUrl?: string
}

const props = defineProps<Props>()

// State
const loading = ref(true)
const error = ref('')

// Methods
const handleLoad = () => {
  loading.value = false
  error.value = ''
  console.log('PDF loaded successfully')
}

const handleError = (event?: Event) => {
  loading.value = false
  console.warn('PDF loading error:', event)
  error.value = '浏览器可能不支持直接显示此PDF文件'
}

const openInNewTab = () => {
  if (props.pdfUrl) {
    window.open(props.pdfUrl, '_blank')
  } else {
    ElMessage.warning('没有可打开的PDF文件')
  }
}

const downloadPdf = () => {
  if (props.pdfUrl) {
    const link = document.createElement('a')
    link.href = props.pdfUrl
    link.download = 'document.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage.warning('没有可下载的PDF文件')
  }
}

// Watchers
watch(() => props.pdfUrl, (newUrl) => {
  if (newUrl) {
    loading.value = true
    error.value = ''
    
    // 设置加载超时
    setTimeout(() => {
      if (loading.value) {
        handleError()
      }
    }, 10000) // 10秒超时
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.pdfUrl) {
    // 初始加载超时
    setTimeout(() => {
      if (loading.value) {
        handleError()
      }
    }, 10000)
  } else {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.embed-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  
  .toolbar-left {
    .pdf-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

.pdf-container {
  flex: 1;
  position: relative;
  min-height: 0;
  background: white;
}

.pdf-loading,
.pdf-error,
.no-pdf {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  color: #666;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #999;
  }
  
  .loading-spin {
    animation: spin 1s linear infinite;
    color: #409eff;
  }
  
  p {
    margin: 8px 0;
    font-size: 14px;
  }
  
  .error-detail {
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
    text-align: center;
    max-width: 300px;
  }
  
  .error-actions {
    display: flex;
    gap: 8px;
  }
}

.pdf-embed {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
