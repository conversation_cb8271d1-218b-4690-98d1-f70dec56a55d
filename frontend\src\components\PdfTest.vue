<template>
  <div class="pdf-test">
    <h2>PDF查看器测试</h2>
    <div class="test-buttons">
      <el-button @click="testLocalPdf" type="primary">测试本地PDF</el-button>
      <el-button @click="testOnlinePdf">测试在线PDF</el-button>
    </div>
    
    <div class="pdf-viewer-container" v-if="currentPdfUrl">
      <NativePdfViewer :pdf-url="currentPdfUrl" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NativePdfViewer from './NativePdfViewer.vue'

const currentPdfUrl = ref('')

const testLocalPdf = () => {
  currentPdfUrl.value = '/test.pdf'
}

const testOnlinePdf = () => {
  // 使用一个公开的PDF文件进行测试
  currentPdfUrl.value = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
}
</script>

<style lang="scss" scoped>
.pdf-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-buttons {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pdf-viewer-container {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}
</style>
