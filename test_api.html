<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Configuration Test</h1>
    <div id="result"></div>
    
    <script>
        // Test environment variable
        console.log('VITE_API_BASE_URL:', import.meta.env?.VITE_API_BASE_URL);
        
        // Test API base URL construction
        const baseURL = import.meta.env?.VITE_API_BASE_URL ? `${import.meta.env.VITE_API_BASE_URL}/api` : '/api';
        console.log('Constructed baseURL:', baseURL);
        
        document.getElementById('result').innerHTML = `
            <p>VITE_API_BASE_URL: ${import.meta.env?.VITE_API_BASE_URL || 'undefined'}</p>
            <p>Constructed baseURL: ${baseURL}</p>
            <p>Upload URL would be: ${baseURL}/documents/upload</p>
        `;
    </script>
</body>
</html>
