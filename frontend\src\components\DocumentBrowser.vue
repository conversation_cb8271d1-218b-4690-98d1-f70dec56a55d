<template>
  <div class="document-browser">
    <!-- Document Viewer (Main Area) -->
    <div class="document-viewer">
      <div v-if="selectedDocument" class="document-content">
        <el-button
          class="close-button"
          link
          size="small"
          @click="clearSelection"
          title="关闭文档"
        >
          <el-icon><close /></el-icon>
        </el-button>

        <el-button
          v-if="selectedDocument.file_type === 'pdf' && selectedDocument.is_processed"
          :class="['viewer-toggle-button', pdfViewerType]"
          @click="togglePdfViewer"
          :title="`切换到${pdfViewerType === 'new' ? '旧' : '新'}版阅读器`"
        >
          {{ pdfViewerType === 'new' ? '新' : '旧' }}
        </el-button>

        <div class="content-scroll">
          <!-- PDF Viewer -->
          <template v-if="selectedDocument.file_type === 'pdf' && selectedDocument.is_processed">
            <NativePdfViewer
              v-show="pdfViewerType === 'native'"
              :pdf-url="getPdfUrl(selectedDocument)"
              @text-selected="handlePdfTextSelection"
            />
            <NewPdfViewer
              v-show="pdfViewerType === 'new'"
              :pdf-url="getPdfUrl(selectedDocument)"
              @text-selected="handlePdfTextSelection"
            />
          </template>

          <!-- Web Viewer -->
          <WebViewer
            v-else-if="
              selectedDocument.file_type === 'url' && selectedDocument.is_processed
            "
            :url="selectedDocument.original_filename"
            :content="selectedDocument.content"
          />

          <!-- Text Content -->
          <el-scrollbar
            v-else-if="selectedDocument.is_processed && selectedDocument.content"
            class="text-scroll"
          >
            <div class="content-text" @mouseup="handleTextSelection">
              <div class="content-body">
                {{ selectedDocument.content }}
              </div>
            </div>
          </el-scrollbar>

          <!-- Processing State -->
          <div v-else-if="!selectedDocument.is_processed" class="processing-placeholder">
            <el-icon class="loading-spin"><loading /></el-icon>
            <p>正在处理文档...</p>
          </div>

          <!-- Empty State -->
          <div v-else class="empty-content">
            <p>无法提取文档内容</p>
          </div>
        </div>
      </div>
      <div v-else class="document-placeholder">
        <el-upload
          ref="uploadRef"
          class="upload-dragger-large"
          drag
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :show-file-list="false"
          :disabled="documentStore.uploading"
          accept=".pdf,.docx,.txt"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">支持 PDF、Word、TXT 格式</div>
          </template>
        </el-upload>
      </div>
    </div>

    <!-- Toggle Header for Management Section -->
    <div class="toggle-header" @click="isManagementSectionVisible = !isManagementSectionVisible">
      <div class="toggle-content">
        <span class="toggle-text">文档管理</span>
        <el-icon class="toggle-icon" :class="{ rotated: !isManagementSectionVisible }">
          <arrow-up />
        </el-icon>
      </div>
    </div>

    <!-- Tabs for Upload and Document List -->
    <div class="management-section" v-show="isManagementSectionVisible">
      <el-tabs type="border-card" class="management-tabs">
        <el-tab-pane label="上传新文档">
          <div class="upload-content">
            <el-upload
              ref="uploadRef"
              class="upload-dragger"
              drag
              :http-request="customUpload"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :disabled="documentStore.uploading"
              accept=".pdf,.docx,.txt"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">支持 PDF、Word、TXT 格式</div>
              </template>
            </el-upload>

            <!-- URL Upload -->
            <div class="url-upload">
              <el-input
                v-model="urlInput"
                placeholder="输入网页链接"
                :disabled="documentStore.uploading"
                @keyup.enter="handleUrlUpload"
              >
                <template #append>
                  <el-button :loading="documentStore.uploading" @click="handleUrlUpload">
                    添加
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="`文档列表 (${documentStore.documentCount})`">
          <div class="document-list">
            <div class="list-actions">
              <el-button
                link
                size="small"
                @click="refreshDocuments"
                :loading="documentStore.loading"
                title="刷新列表"
              >
                <el-icon><refresh /></el-icon>
              </el-button>
            </div>

            <el-scrollbar class="document-scroll">
              <div
                v-if="documentStore.loading && !documentStore.hasDocuments"
                class="loading-placeholder"
              >
                <el-skeleton :rows="2" animated />
              </div>

              <div v-else-if="!documentStore.hasDocuments" class="empty-placeholder">
                <el-empty description="暂无文档" :image-size="60" />
              </div>

              <div v-else class="document-items">
                <div
                  v-for="document in documentStore.documents"
                  :key="document.id"
                  class="document-item"
                  :class="{
                    active: selectedDocumentId === document.id,
                    processing: !document.is_processed,
                  }"
                  @click="selectDocument(document)"
                >
                  <div class="document-icon">
                    <el-icon>
                      <document v-if="document.file_type === 'pdf'" />
                      <edit-pen v-else-if="document.file_type === 'docx'" />
                      <document-copy v-else-if="document.file_type === 'txt'" />
                      <link v-else />
                    </el-icon>
                  </div>

                  <div class="document-info">
                    <div class="document-name" :title="document.original_filename">
                      {{ document.original_filename }}
                    </div>
                    <div class="document-meta">
                      <span class="file-type">{{ document.file_type.toUpperCase() }}</span>
                      <span v-if="document.file_size" class="file-size">
                        {{ formatFileSize(document.file_size) }}
                      </span>
                      <span class="upload-time">
                        {{ formatTime(document.created_at) }}
                      </span>
                    </div>
                  </div>

                  <div class="document-actions">
                    <el-button
                      link
                      size="small"
                      @click.stop="deleteDocument(document.id)"
                      :loading="deletingIds.has(document.id)"
                    >
                      <el-icon><delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  UploadFilled,
  Document,
  EditPen,
  DocumentCopy,
  Link,
  Refresh,
  Delete,
  Close,
  Loading,
  ArrowUp,
} from "@element-plus/icons-vue";
import { useDocumentStore } from "@/stores/document";
import type { Document as DocumentType } from "@/types";
import NativePdfViewer from "./NativePdfViewer.vue";
import NewPdfViewer from "./NewPdfViewer.vue";
import WebViewer from "./WebViewer.vue";

// Emits
const emit = defineEmits<{
  documentSelected: [documentId: number];
  documentCleared: [];
  textSelected: [text: string];
  viewChange: [isViewing: boolean];
  pdfViewerChanged: [viewerType: "native" | "new"];
}>();

// Store
const documentStore = useDocumentStore();

// Refs
const uploadRef = ref<any>(null);

// State
const urlInput = ref("");
const selectedDocumentId = ref<number | null>(null);
const deletingIds = ref<Set<number>>(new Set());
const isManagementSectionVisible = ref(true);
const pdfViewerType = ref<"native" | "new">("new");

// Computed
const selectedDocument = computed(
  () => documentStore.documents.find((doc) => doc.id === selectedDocumentId.value) || null
);

// Methods
const togglePdfViewer = () => {
  pdfViewerType.value = pdfViewerType.value === "new" ? "native" : "new";
  emit("pdfViewerChanged", pdfViewerType.value);
};

const beforeUpload = (file: File) => {
  const isValidType = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/plain",
  ].includes(file.type);

  if (!isValidType) {
    ElMessage.error("只支持 PDF、Word、TXT 格式的文件");
    return false;
  }
  return true;
};

const customUpload = async (options: any) => {
  try {
    const document = await documentStore.uploadFile(options.file);
    ElMessage.success("文件上传成功");
    await selectDocument(document);
    options.onSuccess(document);
  } catch (error) {
    console.error("Upload error:", error);
    ElMessage.error("文件上传失败");
    options.onError(error);
  }
};

const handleUrlUpload = async () => {
  if (!urlInput.value.trim()) {
    ElMessage.warning("请输入网页链接");
    return;
  }

  try {
    const document = await documentStore.uploadUrl(urlInput.value.trim());
    urlInput.value = "";
    ElMessage.success("网页添加成功");
    await selectDocument(document);
  } catch (error) {
    ElMessage.error("网页添加失败");
  }
};

const selectDocument = async (document: DocumentType) => {
  selectedDocumentId.value = document.id;
  await documentStore.selectDocument(document.id);
  emit("documentSelected", document.id);
  emit("viewChange", true);
  // 如果是 PDF 文档，发出当前查看器类型
  if (document.file_type === "pdf") {
    emit("pdfViewerChanged", pdfViewerType.value);
  }
  isManagementSectionVisible.value = false;
};

const deleteDocument = async (documentId: number) => {
  deletingIds.value.add(documentId);
  try {
    await documentStore.deleteDocument(documentId);
    if (selectedDocumentId.value === documentId) {
      selectedDocumentId.value = null;
    }
    ElMessage.success("文档删除成功");
  } catch (error) {
    ElMessage.error("文档删除失败");
  } finally {
    deletingIds.value.delete(documentId);
  }
};

const refreshDocuments = async () => {
  try {
    await documentStore.fetchDocuments();
  } catch (error) {
    ElMessage.error("刷新失败");
  }
};

const clearSelection = () => {
  selectedDocumentId.value = null;
  documentStore.clearCurrentDocument();
  documentStore.clearSelectedText(); // 清除选择的文本
  emit("documentCleared");
  emit("textSelected", ""); // 通知父组件清除选择文本
  emit("viewChange", false);
  isManagementSectionVisible.value = true;
};

const getPdfUrl = (document: DocumentType) => {
  return `http://127.0.0.1:8000/api/documents/${document.id}/file`;
};

const updateSelectedText = (selectedText: string) => {
  documentStore.setSelectedText(selectedText);
  emit("textSelected", selectedText);
  ElMessage.success(`已选择文本: ${selectedText.substring(0, 20)}...`);
};

const handlePdfTextSelection = (selectedText: string) => {
  if (selectedText) {
    updateSelectedText(selectedText);
  } else {
    // 当没有选中文本时，清除之前的选择
    documentStore.clearSelectedText();
    emit("textSelected", "");
  }
};

const handleTextSelection = () => {
  const selection = window.getSelection();
  const selectedText = selection ? selection.toString().trim() : "";
  if (selectedText) {
    updateSelectedText(selectedText);
  } else {
    // 当没有选中文本时，清除之前的选择
    documentStore.clearSelectedText();
    emit("textSelected", "");
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === null || bytes === undefined) return "";
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// Lifecycle
onMounted(async () => {
  try {
    await documentStore.fetchDocuments();
  } catch (error) {
    // Error is already handled in store
  }
});
</script>

<style lang="scss" scoped>
.document-browser {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $panel-left-bg;
  overflow: hidden;
}

.document-viewer {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 12px;
}

.document-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;

  .upload-dragger-large {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
    }
  }
}

.document-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  min-height: 0;

  .close-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    padding: 4px;
    &:hover {
      background: rgba(230, 230, 230, 0.9);
    }
  }

  .viewer-toggle-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 8px;
    z-index: 10;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    &.new {
      background-color: #67c23a;
    }

    &.native {
      background-color: #f56c6c;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .content-scroll {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;

    .text-scroll {
      flex: 1;
    }
  }

  .content-text {
    padding: 20px;
    user-select: text;

    .content-body {
      font-size: 14px;
      line-height: 1.8;
      color: $text-color-primary;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }

  .processing-placeholder,
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px;
    color: $text-color-secondary;

    .loading-spin {
      font-size: 24px;
      margin-bottom: 12px;
      animation: spin 1s linear infinite;
    }
  }
}

.toggle-header {
  background: #f5f7fa;
  border-top: 1px solid $border-color-light;
  border-bottom: 1px solid $border-color-light;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background: #e9e9e9;
  }

  .toggle-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toggle-text {
      font-size: 13px;
      font-weight: 500;
      color: $text-color-primary;
    }

    .toggle-icon {
      font-size: 14px;
      color: $text-color-secondary;
      transition: transform 0.2s ease;

      &.rotated {
        transform: rotate(-180deg);
      }
    }
  }
}

.management-section {
  flex-shrink: 0;
  padding: 0 12px 12px 12px;
}

.management-tabs {
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.upload-content {
  padding: $spacing-lg;
  .upload-dragger {
    margin-bottom: $spacing-md;
  }
}

.document-list {
  display: flex;
  flex-direction: column;
  max-height: 240px;

  .list-actions {
    padding: 4px 12px;
    border-bottom: 1px solid $border-color-lighter;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
  }

  .document-scroll {
    flex: 1;
    min-height: 0;
  }

  .document-items {
    padding: 8px;
  }

  .document-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 4px;
    border: 1px solid transparent;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: $transition-base;

    &:hover {
      background: $document-item-hover-bg;
    }

    &.active {
      background: $document-selected-bg;
      border-color: $document-selected-border;
    }

    &.processing {
      opacity: 0.7;
    }

    .document-icon {
      margin-right: $spacing-md;
      color: $text-color-secondary;
      font-size: 18px;
    }

    .document-info {
      flex: 1;
      min-width: 0;

      .document-name {
        font-size: 13px;
        font-weight: 500;
        color: $text-color-primary;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 4px;
      }

      .document-meta {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        font-size: 11px;
        color: $text-color-secondary;
      }
    }

    .document-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover .document-actions {
      opacity: 1;
    }
  }
}

.loading-placeholder,
.empty-placeholder {
  padding: $spacing-lg;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>