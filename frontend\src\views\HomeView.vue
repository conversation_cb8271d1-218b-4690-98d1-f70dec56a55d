<template>
  <div class="home-container">
    <div class="layout-container">
      <div
        class="panel left-panel"
        :class="{ 'panel-hidden': !leftPanelVisible }"
        :style="{ width: leftPanelVisible ? `${panelWidths.left}%` : '0%' }"
      >
        <div class="panel-header" v-if="!isDocumentViewing">
          <h3>知识浏览区</h3>
          <el-button
            link
            @click="togglePanel('left')"
            :icon="leftPanelVisible ? 'ArrowLeft' : 'ArrowRight'"
          />
        </div>
        <DocumentBrowser
          v-if="leftPanelVisible"
          @document-selected="handleDocumentSelected"
          @document-cleared="handleDocumentCleared"
          @text-selected="handleTextSelected"
          @view-change="handleViewChange"
          @pdf-viewer-changed="handlePdfViewerChanged"
        />
      </div>

      <div
        v-if="leftPanelVisible && middlePanelVisible"
        class="resize-handle left-handle"
        @mousedown="startResize('left')"
      />

      <div
        class="panel middle-panel"
        :class="{ 'panel-hidden': !middlePanelVisible }"
        :style="{ width: middlePanelVisible ? `${panelWidths.middle}%` : '0%' }"
      >
        <div class="panel-header">
          <h3>AI知识问答</h3>
          <div class="header-actions">
            <el-button
              link
              @click="togglePanel('middle')"
              :icon="middlePanelVisible ? 'ArrowLeft' : 'ArrowRight'"
            />
          </div>
        </div>
        <ChatInterface
          v-if="middlePanelVisible"
          :document-id="selectedDocumentId"
          :selected-text="selectedText"
          :pdf-viewer-type="pdfViewerType"
          @text-selected="handleTextSelected"
        />
      </div>

      <div
        v-if="middlePanelVisible && rightPanelVisible"
        class="resize-handle right-handle"
        @mousedown="startResize('right')"
      />

      <div
        class="panel right-panel"
        :class="{ 'panel-hidden': !rightPanelVisible }"
        :style="{ width: rightPanelVisible ? `${panelWidths.right}%` : '0%' }"
      >
        <div class="panel-header">
          <h3>知识检测</h3>
          <el-button
            link
            @click="togglePanel('right')"
            :icon="rightPanelVisible ? 'ArrowRight' : 'ArrowLeft'"
          />
        </div>
        <QuestionPanel v-if="rightPanelVisible" :document-id="selectedDocumentId" />
      </div>
    </div>

    <div class="restore-buttons">
      <el-button
        v-if="!leftPanelVisible"
        class="restore-btn left-restore"
        type="primary"
        circle
        @click="togglePanel('left')"
        :icon="'ArrowRight'"
      />
      <el-button
        v-if="!middlePanelVisible"
        class="restore-btn middle-restore"
        type="primary"
        circle
        @click="togglePanel('middle')"
        :icon="'ArrowRight'"
      />
      <el-button
        v-if="!rightPanelVisible"
        class="restore-btn right-restore"
        type="primary"
        circle
        @click="togglePanel('right')"
        :icon="'ArrowLeft'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue"; // Changed onUnmounted to onUnmount
import DocumentBrowser from "@/components/DocumentBrowser.vue";
import ChatInterface from "@/components/ChatInterface.vue";
import QuestionPanel from "@/components/QuestionPanel.vue";
import { useDocumentStore } from "@/stores/document";

// Store
const documentStore = useDocumentStore();

// 面板可见性状态
const leftPanelVisible = ref(true);
const middlePanelVisible = ref(true);
const rightPanelVisible = ref(true);

// 面板宽度
const panelWidths = reactive({
  left: 30,
  middle: 40,
  right: 30,
});

// 保存的面板宽度（用于恢复）
const savedWidths = reactive({
  left: 30,
  middle: 40,
  right: 30,
});

// 选中的文档和文本
const selectedDocumentId = ref<number | null>(null);
const selectedText = ref("");
const pdfViewerType = ref<"native" | "new">("new");

const isDocumentViewing = ref(false);

// 拖拽相关状态
const isResizing = ref(false);
const resizeType = ref<"left" | "right" | null>(null);
const startMouseX = ref(0);
const initialPanelWidths = reactive({ left: 0, middle: 0, right: 0 });

// 切换面板显示/隐藏
const togglePanel = (panel: "left" | "middle" | "right") => {
  if (panel === "left") {
    if (leftPanelVisible.value) {
      savedWidths.left = panelWidths.left; // Save current width
      panelWidths.left = 0;
      leftPanelVisible.value = false;
      // Distribute the width to middle panel
      panelWidths.middle =
        (middlePanelVisible.value ? panelWidths.middle : 0) + savedWidths.left;
    } else {
      leftPanelVisible.value = true;
      // Revert saved width to left, reduce from middle
      panelWidths.left = savedWidths.left;
      panelWidths.middle = Math.max(0, panelWidths.middle - savedWidths.left);
    }
  } else if (panel === "middle") {
    if (middlePanelVisible.value) {
      savedWidths.middle = panelWidths.middle;
      middlePanelVisible.value = false;
      panelWidths.middle = 0;

      // Distribute middle width to visible side panels proportionally
      const totalSideWidth =
        (leftPanelVisible.value ? panelWidths.left : 0) +
        (rightPanelVisible.value ? panelWidths.right : 0);
      if (totalSideWidth > 0) {
        const leftRatio =
          (leftPanelVisible.value ? panelWidths.left : 0) / totalSideWidth;
        panelWidths.left =
          (leftPanelVisible.value ? panelWidths.left : 0) +
          savedWidths.middle * leftRatio;
        panelWidths.right =
          (rightPanelVisible.value ? panelWidths.right : 0) +
          savedWidths.middle * (1 - leftRatio);
      } else if (leftPanelVisible.value) {
        // If only left is visible
        panelWidths.left += savedWidths.middle;
      } else if (rightPanelVisible.value) {
        // If only right is visible
        panelWidths.right += savedWidths.middle;
      } else {
        // All panels hidden, default to left
        panelWidths.left = 100; // or some default distribution
      }
    } else {
      middlePanelVisible.value = true;
      // Revert middle width, reduce from side panels proportionally
      const targetMiddleWidth = savedWidths.middle;
      const currentAvailableWidth =
        (leftPanelVisible.value ? panelWidths.left : 0) +
        (rightPanelVisible.value ? panelWidths.right : 0);

      if (currentAvailableWidth >= targetMiddleWidth) {
        const leftRatio =
          (leftPanelVisible.value ? panelWidths.left : 0) / currentAvailableWidth;
        panelWidths.left =
          (leftPanelVisible.value ? panelWidths.left : 0) - targetMiddleWidth * leftRatio;
        panelWidths.right =
          (rightPanelVisible.value ? panelWidths.right : 0) -
          targetMiddleWidth * (1 - leftRatio);
        panelWidths.middle = targetMiddleWidth;
      } else {
        // Not enough space in side panels, adjust as much as possible
        panelWidths.middle = currentAvailableWidth;
        panelWidths.left = 0;
        panelWidths.right = 0;
        // You might want to handle this case more robustly, e.g., set minimums
      }
    }
  } else if (panel === "right") {
    if (rightPanelVisible.value) {
      savedWidths.right = panelWidths.right;
      panelWidths.right = 0;
      rightPanelVisible.value = false;
      // Distribute the width to middle panel
      panelWidths.middle =
        (middlePanelVisible.value ? panelWidths.middle : 0) + savedWidths.right;
    } else {
      rightPanelVisible.value = true;
      // Revert saved width to right, reduce from middle
      panelWidths.right = savedWidths.right;
      panelWidths.middle = Math.max(0, panelWidths.middle - savedWidths.right);
    }
  }
  // Ensure widths sum to 100% and are non-negative after toggling
  normalizePanelWidths();
};

const normalizePanelWidths = () => {
  let currentTotal = 0;
  if (leftPanelVisible.value) currentTotal += panelWidths.left;
  if (middlePanelVisible.value) currentTotal += panelWidths.middle;
  if (rightPanelVisible.value) currentTotal += panelWidths.right;

  if (currentTotal > 0 && currentTotal !== 100) {
    const scaleFactor = 100 / currentTotal;
    if (leftPanelVisible.value) panelWidths.left *= scaleFactor;
    if (middlePanelVisible.value) panelWidths.middle *= scaleFactor;
    if (rightPanelVisible.value) panelWidths.right *= scaleFactor;
  }
  // Ensure no negative widths after adjustment
  panelWidths.left = Math.max(0, panelWidths.left);
  panelWidths.middle = Math.max(0, panelWidths.middle);
  panelWidths.right = Math.max(0, panelWidths.right);
};

// 开始拖拽调整
const startResize = (type: "left" | "right") => {
  isResizing.value = true;
  resizeType.value = type;
  startMouseX.value = event.clientX; // Store initial mouse position

  // Store initial panel widths
  initialPanelWidths.left = panelWidths.left;
  initialPanelWidths.middle = panelWidths.middle;
  initialPanelWidths.right = panelWidths.right;

  document.addEventListener("mousemove", handleResize);
  document.addEventListener("mouseup", stopResize);
  document.body.style.userSelect = "none"; // Prevent text selection during drag
  document.body.style.cursor = "col-resize"; // Change cursor during drag
};

// 处理拖拽
const handleResize = (e: MouseEvent) => {
  if (!isResizing.value || !resizeType.value) return;

  const container = document.querySelector(".layout-container") as HTMLElement;
  if (!container) return;

  const containerWidth = container.getBoundingClientRect().width;
  const deltaX = e.clientX - startMouseX.value; // Calculate mouse movement
  const deltaPercentage = (deltaX / containerWidth) * 100; // Convert to percentage

  const MIN_WIDTH = 10; // Minimum width for a visible panel

  if (resizeType.value === "left") {
    let newLeftWidth = initialPanelWidths.left + deltaPercentage;
    let newMiddleWidth = initialPanelWidths.middle - deltaPercentage;

    // Clamp widths to prevent collapsing or excessive expansion
    newLeftWidth = Math.max(MIN_WIDTH, newLeftWidth);
    newMiddleWidth = Math.max(MIN_WIDTH, newMiddleWidth);

    // If one panel hits its minimum, adjust the other accordingly to maintain total
    if (newLeftWidth > 100 - panelWidths.right - MIN_WIDTH) {
      // Left panel too wide
      newLeftWidth = 100 - panelWidths.right - MIN_WIDTH;
      newMiddleWidth = MIN_WIDTH;
    }
    if (newMiddleWidth > 100 - panelWidths.left - MIN_WIDTH) {
      // Middle panel too wide
      newMiddleWidth = 100 - panelWidths.left - MIN_WIDTH;
      newLeftWidth = MIN_WIDTH;
    }

    // Ensure they don't exceed the total available space with the right panel
    const combinedWidth = newLeftWidth + newMiddleWidth + panelWidths.right;
    if (combinedWidth !== 100) {
      const adjustment = (100 - combinedWidth) / 2; // Distribute adjustment
      newLeftWidth += adjustment;
      newMiddleWidth += adjustment;
    }

    panelWidths.left = newLeftWidth;
    panelWidths.middle = newMiddleWidth;
  } else if (resizeType.value === "right") {
    let newRightWidth = initialPanelWidths.right - deltaPercentage; // Inverse for right handle
    let newMiddleWidth = initialPanelWidths.middle + deltaPercentage;

    // Clamp widths
    newRightWidth = Math.max(MIN_WIDTH, newRightWidth);
    newMiddleWidth = Math.max(MIN_WIDTH, newMiddleWidth);

    // If one panel hits its minimum, adjust the other accordingly
    if (newRightWidth > 100 - panelWidths.left - MIN_WIDTH) {
      // Right panel too wide
      newRightWidth = 100 - panelWidths.left - MIN_WIDTH;
      newMiddleWidth = MIN_WIDTH;
    }
    if (newMiddleWidth > 100 - panelWidths.right - MIN_WIDTH) {
      // Middle panel too wide
      newMiddleWidth = 100 - panelWidths.right - MIN_WIDTH;
      newRightWidth = MIN_WIDTH;
    }

    // Ensure they don't exceed the total available space with the left panel
    const combinedWidth = panelWidths.left + newMiddleWidth + newRightWidth;
    if (combinedWidth !== 100) {
      const adjustment = (100 - combinedWidth) / 2; // Distribute adjustment
      newMiddleWidth += adjustment;
      newRightWidth += adjustment;
    }

    panelWidths.middle = newMiddleWidth;
    panelWidths.right = newRightWidth;
  }

  // Distribute any leftover percentage due to clamping or rounding errors
  const currentTotal = panelWidths.left + panelWidths.middle + panelWidths.right;
  if (Math.abs(currentTotal - 100) > 0.1) {
    // Check for small discrepancies
    const diff = 100 - currentTotal;
    // Distribute the difference proportionally to active panels
    const activePanels = [
      leftPanelVisible.value ? "left" : null,
      middlePanelVisible.value ? "middle" : null,
      rightPanelVisible.value ? "right" : null,
    ].filter(Boolean);

    if (activePanels.length > 0) {
      const perPanelAdjustment = diff / activePanels.length;
      activePanels.forEach((p) => {
        if (p) panelWidths[p] += perPanelAdjustment;
      });
    }
  }

  // Ensure no negative widths
  panelWidths.left = Math.max(0, panelWidths.left);
  panelWidths.middle = Math.max(0, panelWidths.middle);
  panelWidths.right = Math.max(0, panelWidths.right);
};

// 停止拖拽
const stopResize = () => {
  isResizing.value = false;
  resizeType.value = null;
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResize);
  document.body.style.userSelect = ""; // Reset user-select
  document.body.style.cursor = ""; // Reset cursor
};

// 处理文档选择
const handleDocumentSelected = (documentId: number) => {
  selectedDocumentId.value = documentId;
};

// 处理文档清除
const handleDocumentCleared = () => {
  selectedDocumentId.value = null;
  selectedText.value = ""; // 同时清除选择的文本
};

// 处理文本选择
const handleTextSelected = (text: string) => {
  selectedText.value = text;
  documentStore.setSelectedText(text);
};

const handleViewChange = (isViewing: boolean) => {
  isDocumentViewing.value = isViewing;
};

const handlePdfViewerChanged = (viewerType: "native" | "new") => {
  pdfViewerType.value = viewerType;
};

// 清理事件监听器
onUnmounted(() => {
  // Changed to onUnmount
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResize);
});

// Initial width normalization on mount, though reactive setup might handle this
onMounted(() => {
  normalizePanelWidths();
});
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.panel {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e4e7ed;
  transition: width 0s ease; /* Set transition to 0s for instant resizing */
  position: relative;
  box-sizing: border-box; /* Ensure padding/border is included in width */

  &.panel-hidden {
    overflow: hidden;
    width: 0 !important; /* Ensure hidden panel width is 0 */
  }

  &:last-child {
    border-right: none;
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  min-height: 48px;
  flex-shrink: 0; /* Prevent header from shrinking */

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.resize-handle {
  width: 4px;
  background: #e4e7ed;
  cursor: col-resize;
  transition: background 0.2s;
  flex-shrink: 0; /* Prevent handle from shrinking */

  &:hover {
    background: #409eff;
  }

  &.left-handle {
    // No specific order needed with flexbox by default, but keeping it won't hurt
  }

  &.right-handle {
    // No specific order needed with flexbox by default
  }
}

.restore-buttons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

.restore-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: all;
  z-index: 1001;

  &.left-restore {
    left: 8px;
  }

  &.middle-restore {
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &.right-restore {
    right: 8px;
  }
}

.left-panel {
  background: #fafbfc;
}

.middle-panel {
  background: #ffffff;
}

.right-panel {
  background: #f9f9f9;
}
</style>
