import axios from 'axios'
import type {
  Document,
  ChatMessage,
  Conversation,
  Question,
  QuestionWithAnswer,
  AnswerResponse,
  ChatRequest,
  GenerateQuestionsRequest,
  GenerateQuestionsResponse,
  AnswerRequest
} from '@/types'

// Create axios instance
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000/api',
  timeout: 120000, // 增加到2分钟，因为AI生成问题可能需要较长时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if needed
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// Document API
export const documentAPI = {
  // Upload file
  async uploadFile(file: File): Promise<Document> {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // Upload URL
  async uploadUrl(url: string, title?: string): Promise<Document> {
    return api.post('/documents/upload-url', { url, title })
  },

  // Get document by ID
  async getDocument(id: number): Promise<Document> {
    return api.get(`/documents/${id}`)
  },

  // List documents
  async listDocuments(skip = 0, limit = 100): Promise<Document[]> {
    return api.get('/documents/', { params: { skip, limit } })
  },

  // Delete document
  async deleteDocument(id: number): Promise<{ message: string }> {
    return api.delete(`/documents/${id}`)
  }
}

// Chat API
export const chatAPI = {
  // Send message (non-streaming)
  async sendMessage(request: ChatRequest): Promise<{ message: string; conversation_id: number; message_id: number }> {
    return api.post('/chat/send', request)
  },

  // Stream message
  async streamMessage(request: ChatRequest): Promise<ReadableStream> {
    const response = await fetch('http://127.0.0.1:8000/api/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    })

    if (!response.body) {
      throw new Error('No response body')
    }

    return response.body
  },

  // List conversations
  async listConversations(skip = 0, limit = 50): Promise<Conversation[]> {
    return api.get('/chat/conversations', { params: { skip, limit } })
  },

  // Get conversation messages
  async getConversationMessages(conversationId: number): Promise<ChatMessage[]> {
    return api.get(`/chat/conversations/${conversationId}/messages`)
  },

  // Delete conversation
  async deleteConversation(conversationId: number): Promise<{ message: string }> {
    return api.delete(`/chat/conversations/${conversationId}`)
  }
}

// Questions API
export const questionsAPI = {
  // Generate questions
  async generateQuestions(request: GenerateQuestionsRequest): Promise<GenerateQuestionsResponse> {
    return api.post('/questions/generate', request, {
      timeout: 180000 // 3分钟超时，专门为问题生成设置
    })
  },

  // List questions
  async listQuestions(params: {
    document_id?: number
    question_type?: string
    difficulty?: string
    skip?: number
    limit?: number
  } = {}): Promise<Question[]> {
    return api.get('/questions/', { params })
  },

  // Submit answer
  async submitAnswer(request: AnswerRequest): Promise<AnswerResponse> {
    return api.post('/questions/answer', request)
  },

  // Get question with answer
  async getQuestion(id: number): Promise<QuestionWithAnswer> {
    return api.get(`/questions/${id}`)
  },

  // Get statistics
  async getStats(): Promise<{
    total_questions: number
    total_answered: number
    correct_answers: number
    accuracy_rate: number
  }> {
    return api.get('/questions/stats/summary')
  },

  // Delete question
  async deleteQuestion(id: number): Promise<{ message: string }> {
    return api.delete(`/questions/${id}`)
  }
}

export default api
