import os
import uuid
import aiofiles
from typing import Optional
from fastapi import UploadFile
import PyPDF2
import docx
import requests
from bs4 import BeautifulSoup
from sqlalchemy.orm import Session

from app.models.document import Document
from app.core.config import settings

class DocumentService:
    
    @staticmethod
    async def save_uploaded_file(file: UploadFile) -> str:
        """Save uploaded file to disk and return file path"""
        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1].lower()
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        return file_path
    
    @staticmethod
    def extract_text_from_pdf(file_path: str) -> str:
        """Extract text content from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            print(f"Error extracting PDF text: {e}")
            return ""
    
    @staticmethod
    def extract_text_from_docx(file_path: str) -> str:
        """Extract text content from Word document"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error extracting DOCX text: {e}")
            return ""
    
    @staticmethod
    def extract_text_from_txt(file_path: str) -> str:
        """Extract text content from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    return file.read().strip()
            except Exception as e:
                print(f"Error reading TXT file: {e}")
                return ""
        except Exception as e:
            print(f"Error extracting TXT text: {e}")
            return ""
    
    @staticmethod
    def extract_text_from_url(url: str) -> str:
        """Extract text content from web page"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
        except Exception as e:
            print(f"Error extracting URL text: {e}")
            return ""
    
    @staticmethod
    async def process_document(db: Session, document: Document) -> Document:
        """Process document and extract text content"""
        content = ""
        
        if document.file_type == "pdf":
            content = DocumentService.extract_text_from_pdf(document.file_path)
        elif document.file_type == "docx":
            content = DocumentService.extract_text_from_docx(document.file_path)
        elif document.file_type == "txt":
            content = DocumentService.extract_text_from_txt(document.file_path)
        elif document.file_type == "url":
            content = DocumentService.extract_text_from_url(document.url)
        
        # Update document with extracted content
        document.content = content
        document.is_processed = True
        db.commit()
        db.refresh(document)
        
        return document
    
    @staticmethod
    def get_document_by_id(db: Session, document_id: int) -> Optional[Document]:
        """Get document by ID"""
        return db.query(Document).filter(Document.id == document_id).first()
    
    @staticmethod
    def delete_document(db: Session, document_id: int) -> bool:
        """Delete document and associated file"""
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return False
        
        # Delete file from disk if exists
        if document.file_path and os.path.exists(document.file_path):
            try:
                os.remove(document.file_path)
            except Exception as e:
                print(f"Error deleting file: {e}")
        
        # Delete from database
        db.delete(document)
        db.commit()
        return True


# Global document service instance
document_service = DocumentService()
