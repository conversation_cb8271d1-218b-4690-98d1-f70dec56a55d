import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # API Keys
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL: str = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
    
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./knowledge_assistant.db")
    
    # File Upload
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "52428800"))  # 50MB
    
    # Allowed file types
    ALLOWED_EXTENSIONS = {".pdf", ".docx", ".txt"}
    
    # AI Settings
    MAX_TOKENS: int = 4000
    TEMPERATURE: float = 0.7
    
    # Chat Settings
    MAX_CONTEXT_LENGTH: int = 8000
    MAX_HISTORY_MESSAGES: int = 20

settings = Settings()
