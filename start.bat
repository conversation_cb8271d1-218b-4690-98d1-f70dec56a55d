@echo off
echo 🚀 Starting Knowledge Assistant...

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is required but not installed.
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is required but not installed.
    pause
    exit /b 1
)

REM Start backend
echo 📡 Starting backend server...
cd backend

if not exist "venv" (
    echo 🔧 Creating virtual environment...
    python -m venv venv
)

call venv\Scripts\activate

echo 📦 Installing backend dependencies...
pip install -r requirements.txt

echo 🗄️ Initializing database...
python -c "from app.core.database import engine, Base; from app.models import document, conversation, question; Base.metadata.create_all(bind=engine); print('Database initialized successfully!')"

echo 🌐 Starting FastAPI server on http://localhost:8000
start "Backend Server" cmd /k "python main.py"

cd ..

REM Start frontend
echo 🎨 Starting frontend server...
cd frontend

echo 📦 Installing frontend dependencies...
call npm install

echo 🌐 Starting Vue.js server on http://localhost:5173
start "Frontend Server" cmd /k "npm run dev"

cd ..

echo.
echo 🎉 Knowledge Assistant is now running!
echo.
echo 📖 Access the application:
echo    Frontend: http://localhost:5173
echo    Backend API: http://localhost:8000
echo    API Docs: http://localhost:8000/docs
echo.
echo 🛑 Close the terminal windows to stop the servers
echo.
pause
